<?php
/**
 * AJAX handler untuk export data mapping visitasi ke Excel
 */

require_once '../../koneksi.php';
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

header('Content-Type: application/json');

try {
    $provinsi_id = $_SESSION['provinsi_id'];

    // Get tahun aktif dari mapping_2024_tahun
    $tahun_query = "SELECT nama_tahun FROM mapping_2024_tahun WHERE provinsi_id = ?";
    $tahun_stmt = $conn->prepare($tahun_query);
    $tahun_stmt->bind_param("i", $provinsi_id);
    $tahun_stmt->execute();
    $tahun_result = $tahun_stmt->get_result();

    $tahun_aktif = '';
    if ($tahun_result && $tahun_result->num_rows > 0) {
        $tahun_row = $tahun_result->fetch_assoc();
        $tahun_aktif = $tahun_row['nama_tahun'];
    }

    // Query sesuai dengan yang diberikan
    $query = "SELECT mapping_2024.*, sekolah.npsn, sekolah.nama_sekolah, sekolah.rumpun, jenjang.nm_jenjang,
                     mapping_2024.tahun_akreditasi, mapping_2024_tahun.nama_tahun, kab_kota.nm_kota,
                     asesor_1.nia1, asesor_1.nm_asesor1, asesor_1.no_hp as hp1,
                     (SELECT kab_kota.nm_kota from asesor_1 LEFT JOIN kab_kota ON asesor_1.kota_id1=kab_kota.kota_id
                      WHERE mapping_2024.kd_asesor1=asesor_1.kd_asesor1) as kota1,
                     asesor_2.nia2, asesor_2.nm_asesor2, asesor_2.no_hp as hp2,
                     (SELECT kab_kota.nm_kota from asesor_2 LEFT JOIN kab_kota ON asesor_2.kota_id2=kab_kota.kota_id
                      WHERE mapping_2024.kd_asesor2=asesor_2.kd_asesor2) as kota2
              FROM mapping_2024
              LEFT JOIN sekolah ON mapping_2024.sekolah_id=sekolah.sekolah_id
              LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
              LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
              LEFT JOIN asesor_1 ON mapping_2024.kd_asesor1=asesor_1.kd_asesor1
              LEFT JOIN asesor_2 ON mapping_2024.kd_asesor2=asesor_2.kd_asesor2
              LEFT JOIN mapping_2024_tahun ON mapping_2024.tahun_akreditasi=mapping_2024_tahun.nama_tahun
              WHERE sekolah.rumpun = 'dasmen'
              AND mapping_2024.tahun_akreditasi = mapping_2024_tahun.nama_tahun
              AND mapping_2024_tahun.provinsi_id = '$provinsi_id'
              AND mapping_2024.provinsi_id = '$provinsi_id'
              ORDER BY sekolah.nama_sekolah ASC";

    $result = $conn->query($query);

    if (!$result) {
        throw new Exception('Query Error: ' . $conn->error);
    }

    $data = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
        }
    }

    // Log export activity
    error_log("Export Mapping Visitasi SM - Provinsi: $provinsi_id, Tahun: $tahun_aktif, Records: " . count($data) . ", User: " . $_SESSION['nm_user']);

    // Response
    echo json_encode([
        'success' => true,
        'data' => $data,
        'tahun_aktif' => $tahun_aktif,
        'total_records' => count($data),
        'message' => 'Data export berhasil dimuat'
    ]);

} catch (Exception $e) {
    error_log("Export Mapping Visitasi Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));

    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat mengambil data export: ' . $e->getMessage(),
        'data' => [],
        'total_records' => 0
    ]);
}

$conn->close();
?>
