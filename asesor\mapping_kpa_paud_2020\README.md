# Modul Mapping KPA PAUD 2020

## 📋 **OVERVIEW**

Modul ini menampilkan data mapping KPA (Komite Penilaian Akreditasi) PAUD untuk asesor yang sedang login. Asesor dapat melihat sekolah-sekolah PAUD yang telah di-assign kepada mereka untuk proses akreditasi.

## 📁 **STRUKTUR FILE**

```
asesor/mapping_kpa_paud_2020/
├── mapping_kpa.php              # Main module file
├── ajax/
│   └── get_mapping_data.php     # AJAX handler (placeholder)
├── js/
│   └── mapping_kpa.js          # JavaScript functionality
└── README.md                    # This documentation
```

## 🎯 **FITUR UTAMA**

### **1. Tabel Data Mapping**
- **NO**: Nomor urut dengan pagination
- **SEKOLAH**: Informasi lengkap sekolah (NPSN, Nama, Jenjang, Kab/Kota, Kepsek, HP)
- **ASESOR KPA**: Informasi asesor (NIA, Nama, Kab/Kota)
- **DOKUMEN UNGGAHAN**: Tombol upload file hasil KPA
- **PREVIEW**: Status upload (Sudah/Belum Upload)
- **PELAKSANAAN KEGIATAN**: Tanggal penetapan KPA
- **TAHAP**: Tahap akreditasi

### **2. Pagination**
- Menampilkan 10 data per halaman
- Navigation Previous/Next
- Info jumlah data yang ditampilkan

### **3. Responsive Design**
- Table responsive untuk mobile
- Bootstrap styling
- Custom CSS untuk enhancement

## 🔍 **DATABASE QUERY**

### **Main Query:**
```sql
SELECT 
    m.id_mapping, 
    m.sekolah_id, 
    s.npsn, 
    s.nama_sekolah, 
    s.nama_kepsek, 
    s.no_hp_kepsek, 
    j.nm_jenjang, 
    ks.nm_kota as kota_sekolah,
    a.nia, 
    a.nm_asesor, 
    ka.nm_kota as kota_asesor,
    m.tahap, 
    m.tgl_penetapan_kpa,
    m.file_laporan_hasil_kpa,
    m.tahun_akreditasi
FROM mapping_paud_kpa m
LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
LEFT JOIN kab_kota ks ON s.kota_id = ks.kota_id
LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
LEFT JOIN kab_kota ka ON a.kota_id = ka.kota_id
WHERE a.kd_asesor = ? 
    AND s.rumpun = 'paud' 
    AND m.provinsi_id = ?
ORDER BY m.id_mapping DESC
LIMIT ? OFFSET ?
```

### **Count Query:**
```sql
SELECT COUNT(*) as total 
FROM mapping_paud_kpa m
LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
WHERE a.kd_asesor = ? 
    AND s.rumpun = 'paud' 
    AND m.provinsi_id = ?
```

## 🎨 **UI COMPONENTS**

### **Table Structure:**
- **Header**: Dark theme dengan background #343a40
- **Body**: Striped rows dengan hover effect
- **Badges**: Color-coded status (Success/Danger)
- **Buttons**: Primary blue untuk upload

### **Status Indicators:**
- **Sudah Upload**: Green badge (`badge-success`)
- **Belum Upload**: Red badge (`badge-danger`)
- **Tahap**: Blue badge (`badge-info`)

### **Responsive Features:**
- Horizontal scroll pada mobile
- Compact layout untuk small screens
- Touch-friendly buttons

## 🔧 **SESSION REQUIREMENTS**

### **Required Session Variables:**
- `$_SESSION['kd_user']` - Kode asesor yang login
- `$_SESSION['provinsi_id']` - ID provinsi asesor
- `$_SESSION['level']` - Harus 'Asesor'

### **Access Control:**
- Hanya asesor yang bisa mengakses
- Data filtered berdasarkan kd_asesor dan provinsi_id
- Redirect ke login jika tidak authorized

## 📱 **JavaScript Functionality**

### **Current Features:**
- Page initialization
- Event listeners setup
- Upload file placeholder (SweetAlert2)
- Loading states management
- Success/Error message handling

### **Functions Available:**
- `uploadFile(idMapping)` - Upload file handler
- `showLoading()` - Show loading overlay
- `hideLoading()` - Hide loading overlay
- `showSuccess(message)` - Success notification
- `showError(message)` - Error notification
- `refreshData()` - Refresh page data

## 🚀 **FUTURE ENHANCEMENTS**

### **Next Session (Upload Modal):**
- Modal form untuk upload file
- File validation (PDF only)
- Progress bar upload
- File preview functionality
- AJAX upload handler

### **Potential Features:**
- Search/Filter functionality
- Export to Excel
- Bulk operations
- Real-time notifications
- File download links

## 🔍 **TESTING**

### **Test Cases:**
1. **Data Display**: Verify all columns show correct data
2. **Pagination**: Test navigation between pages
3. **Upload Button**: Verify SweetAlert2 popup
4. **Responsive**: Test on mobile devices
5. **Session**: Test access control

### **Test Data Requirements:**
- Asesor dengan kd_asesor di session
- Data di tabel mapping_paud_kpa
- Relasi dengan tabel sekolah, jenjang, kab_kota
- Data dengan dan tanpa file_laporan_hasil_kpa

## 📋 **TROUBLESHOOTING**

### **Common Issues:**
1. **No Data**: Check session variables dan database relations
2. **Pagination Error**: Verify LIMIT/OFFSET calculations
3. **Upload Button**: Ensure SweetAlert2 is loaded
4. **Styling Issues**: Check Bootstrap and custom CSS

### **Debug Steps:**
1. Check session variables: `var_dump($_SESSION)`
2. Test query manually in database
3. Check browser console for JavaScript errors
4. Verify file permissions

---
**Version**: 1.0.0  
**Status**: Ready for Testing  
**Next**: Upload Modal Implementation  
**Last Updated**: 2024-12-19
