<?php
/**
 * AJAX handler untuk save tanggal visitasi
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Asesor', '../../../login.php');

header('Content-Type: application/json');

try {
    // Validasi request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Validasi input
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID Mapping tidak valid');
    }
    
    if (!isset($_POST['tgl_visitasi']) || empty($_POST['tgl_visitasi'])) {
        throw new Exception('Tanggal visitasi tidak valid');
    }
    
    // Get data from POST
    $id_mapping = mysqli_real_escape_string($conn, $_POST['id_mapping']);
    $tgl_visitasi = mysqli_real_escape_string($conn, $_POST['tgl_visitasi']);
    
    // Get session variables
    $kd_user = mysqli_real_escape_string($conn, $_SESSION['kd_user'] ?? '');
    $provinsi_id = mysqli_real_escape_string($conn, $_SESSION['provinsi_id'] ?? '');
    
    // Cek apakah mapping exists dan user adalah asesor1 (hanya asesor1 yang bisa input tanggal)
    $check_query = "SELECT mv.kd_asesor1, mv.kd_asesor2
                    FROM mapping_paud_visitasi mv
                    WHERE mv.id_mapping = '$id_mapping' 
                        AND mv.kd_asesor1 = '$kd_user'
                        AND mv.provinsi_id = '$provinsi_id'";
    
    $check_result = mysqli_query($conn, $check_query);
    
    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses untuk input tanggal');
    }
    
    // Update tanggal visitasi
    $update_query = "UPDATE mapping_paud_visitasi 
                     SET tgl_mulai_visitasi = '$tgl_visitasi'
                     WHERE id_mapping = '$id_mapping'";
    
    $update_result = mysqli_query($conn, $update_query);
    
    if (!$update_result) {
        $db_error = mysqli_error($conn);
        throw new Exception('Gagal menyimpan tanggal visitasi: ' . $db_error);
    }
    
    $affected_rows = mysqli_affected_rows($conn);
    
    if ($affected_rows === 0) {
        throw new Exception('Tidak ada data yang diupdate');
    }
    
    // Response sukses
    echo json_encode([
        'success' => true,
        'message' => 'Tanggal visitasi berhasil disimpan',
        'data' => [
            'id_mapping' => $id_mapping,
            'tgl_visitasi' => $tgl_visitasi,
            'formatted_date' => date('d-m-Y', strtotime($tgl_visitasi))
        ]
    ]);
    
} catch (Exception $e) {
    // Response error
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
