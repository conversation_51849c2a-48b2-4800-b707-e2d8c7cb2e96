Berikut ini adalah struktur tabel "mapping_paud_visitasi" :
id_mapping  int(11)
sekolah_id  int(11)
kd_asesor1  varchar(25)
kd_asesor2  varchar(25)
tgl_mulai_visitasi  date
tgl_akhir_visitasi  date
tahap int(11)
no_surat  varchar(30)
tgl_surat date
tahun_akreditasi  varchar(4)
file_pakta_integritas_1 varchar(50)
file_pakta_integritas_2 varchar(50)
file_berita_acara_visitasi  varchar(50)
file_temuan_hasil_visitasi  varchar(50)
file_absen_pembuka  varchar(50)
file_absen_penutup  varchar(50)
file_foto_visitasi  varchar(50)
tgl_file_foto_visitasi  date
jam_file_foto_visitasi  time
file_laporan_individu_1 varchar(50)
file_laporan_individu_2 varchar(50)
file_laporan_kelompok varchar(50)
file_penjelasan_hasil_akreditasi varchar(50)
provinsi_id int(11)

Berikut ini struktur tabel "sekolah" :
sekolah_id int(11)
nama_sekolah varchar(100)
npsn varchar(30)
jenjang_id int(1)
rumpun varchar(20)
alamat text
tipe_sekolah_id int(1)
status_sekolah_id int(1)
provinsi_id int(2)
kota_id int(6)
desa_kelurahan varchar(200)
kecamatan varchar(100)
nama_kepsek varchar(100)
no_hp_kepsek varchar(50)
no_wa_kepsek varchar(50)
nama_operator varchar(50)
no_hp_operator varchar(20)
no_wa_operator varchar(20)
email varchar(100)
nama_yayasan varchar(200)
no_akte varchar(50)
tahun_berdiri varchar(4)
status_keaktifan_id varchar(1)
soft_delete rchar(1)

Berikut ini adalah struktur tabel "jenjang" :
id_jenjang int(11)
jenjang_id varchar(2)
nm_jenjang varchar(15)

Berikut ini struktur tabel "kab_kota" :
id_kota int(11) 
kota_id varchar(10)
nm_kota varchar(50)
provinsi_id int(11)
kd_user varchar(25)

Berikut ini struktur tabel "asesor_1" :
id_asesor1 int(11)
kd_asesor1 varchar(25)
nia1 varchar(20) 
nm_asesor1  varchar(100) 
ktp varchar(20) 
unit_kerja  varchar(300) 
kota_id1 varchar(10)
provinsi_id int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan varchar(50)
jabatan_struktural varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id varchar(1)
sebab text
kd_user varchar(50)
soft_delete varchar(1)

Berikut ini tabel "asesor_2" :
id_asesor2 int(11)
kd_asesor2 varchar(25)
nia2 varchar(20)
nm_asesor2 varchar(100)
ktp varchar(20)
unit_kerja varchar(300)
kota_id2 varchar(10)
provinsi_id int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan varchar(50)
jabatan_struktural  varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id varchar(1)
sebab text
kd_user varchar(50)
soft_delete varchar(1)

Berikut ini struktur tabel "mapping_paud_visitasi_tahun" :
id_mapping_tahun  int(11)
nama_tahun  int(4)
provinsi_id int(11)

=================================================================================================================

buatlah modul "Mapping Visitasi Paud" pada direktori asesor/mapping_paud_visitasi_2020/mapping_paud_visitasi.php, juga tersedia sub direktori ajax dan js, tabel header yang digunakan adalah :

- NO;

- SEKOLAH, pada tabel body berisi data sebagai berikut : 
  NPSN : sekolah.npsn, NAMA : sekolah.nama_sekolah,
  JENJANG : jenjang.jenjang_id=sekolah.jenjang_id (jenjang.nm_jenjang),
  KAB/KOTA : sekolah.kota_id = kab_kota.kota_id (kab_kota.nm_kota),
  NAMA KEPSEK : sekolah.nama_kepsek,
  NO HP KEPSEK : sekolah.no_hp_kepsek,
  TAHAP VISITASI : mapping_paud_visitasi.tahap

- ASESOR VISITASI, pada tabel body berisi 
  ASESOR A (colspan =2), NIA : asesor_1.nia1, NAMA : asesor_1.nm_asesor1, KAB/KOTA : asesor_1.kota_id1 = kab_kota.kota_id (kab_kota.nm_kota),
  ASESOR B (colspan =2), NIA : asesor_2.nia2, NAMA : asesor_2.nm_asesor2, KAB/KOTA : asesor_2.kota_id2 = kab_kota.kota_id (kab_kota.nm_kota);

- FORM UPLOAD DOKUMEN
  Tombol "Upload File Pakta Integritas Asesor A" hanya tampil jika session login $kd_user==$asesor1
  Tombol "Upload File Pakta Integritas Asesor B" hanya tampil jika session login $kd_user==$asesor2
  Tombol "Upload File Berita Acara Visitasi" hanya tampil jika session login $kd_user==$asesor1
  Tombol "Upload File Temuan Hasil Visitasi" session login $kd_user==$asesor1
  Tombol "Upload File Absen Pembuka" hanya tampil jika session login $kd_user==$asesor1
  Tombol "Upload File Absen Penutup" hanya tampil jika session login $kd_user==$asesor1
  Tombol "Upload File Foto Visitasi" hanya tampil jika session login $kd_user==$asesor1
  Tombol "Upload File Laporan Individu A" hanya tampil jika session login $kd_user==$asesor1
  Tombol "Upload File Laporan Individu B" hanya tampil jika session login $kd_user==$asesor2
  Tombol "Upload File Laporan Kelompok" hanya tampil jika session login $kd_user==$asesor1
  Tombol "Upload File Penjelasan Hasil Akreditasi (PHA)" hanya tampil jika session login $kd_user==$asesor1


- DOKUMEN UNGGAHAN pada tabel body berisi

  label "File Pakta Integritas Asesor A :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_pakta_integritas_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

  label "File Pakta Integritas Asesor B :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_pakta_integritas_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

  label "File Berita Acara Visitasi :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_berita_acara_visitasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

  label "File Temuan Hasil Visitasi :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_temuan_hasil_visitasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

  label "File Absen Pembuka :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_absen_pembuka ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

  label "File Absen Penutup :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_absen_penutup ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

  label "File Foto Visitasi :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_foto_visitasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

  label "File Laporan Individu A :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_laporan_individu_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

  label "File Laporan Individu B :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_laporan_individu_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

  label "File Laporan Kelompok :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_laporan_kelompok ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

  label "File Penjelasan Hasil Akreditasi (PHA) :" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_penjelasan_hasil_akreditasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah


- JADWAL DAN AKSI pada tabel body berisi label "Tanggal Visitasi : mapping_paud_visitasi.tgl_mulai_visitasi dan dibawahnya tombol "Input Tanggal Visitasi" dan dibawahnya lagi tombol "Download Surat Tugas Visitasi" kemudian di bawahnya lagi terdapat tombol "Upload File"


oh iya lupa, tambahkan
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Asesor
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>

berikut di bawah ini query sql yang bisa anda gunakan untuk menampilkan data:

$tahun = "SELECT mapping_paud_visitasi_tahun.nama_tahun FROM mapping_paud_visitasi_tahun WHERE mapping_paud_visitasi_tahun.provinsi_id='$provinsi_id' ";
$result_tahun = $connect->query($tahun);
if($result_tahun->num_rows > 0) {
while($row_tahun = $result_tahun->fetch_assoc()) {
    $nama_tahun = $row_tahun['nama_tahun'];


$sql = "SELECT mapping_paud_visitasi.id_mapping, sekolah.sekolah_id, sekolah.npsn, sekolah.nama_sekolah,
        sekolah.nama_kepsek, 
        sekolah.no_hp_kepsek, 
        sekolah.no_wa_kepsek, 
        sekolah.jenjang_id, 
        jenjang.nm_jenjang,
        sekolah.kota_id, 
        kab_kota.nm_kota, 
        mapping_paud_visitasi.kd_asesor1, 
        mapping_paud_visitasi.kd_asesor2,
        mapping_paud_visitasi.tahap,
        mapping_paud_visitasi.file_pakta_integritas_1, 
        mapping_paud_visitasi.file_pakta_integritas_2,
        mapping_paud_visitasi.file_berita_acara_visitasi, 
        mapping_paud_visitasi.file_temuan_hasil_visitasi,
        mapping_paud_visitasi.file_absen_pembuka, 
        mapping_paud_visitasi.file_absen_penutup,
        mapping_paud_visitasi.file_foto_visitasi,
        mapping_paud_visitasi.file_laporan_individu_1, 
        mapping_paud_visitasi.file_laporan_individu_2,
        mapping_paud_visitasi.file_laporan_kelompok, 
        mapping_paud_visitasi.file_penjelasan_hasil_akreditasi,
        (SELECT asesor.nia from asesor WHERE mapping_paud_visitasi.kd_asesor1=asesor.kd_asesor) as nia1,
        (SELECT asesor.nm_asesor from asesor WHERE mapping_paud_visitasi.kd_asesor1=asesor.kd_asesor) as nama1,
        (SELECT asesor.no_hp from asesor WHERE mapping_paud_visitasi.kd_asesor1=asesor.kd_asesor) as hp1,
        (SELECT kab_kota.nm_kota from asesor LEFT JOIN kab_kota ON asesor.kota_id=kab_kota.kota_id
            WHERE mapping_paud_visitasi.kd_asesor1=asesor.kd_asesor) as kota1,
        (SELECT asesor.nia from asesor WHERE mapping_paud_visitasi.kd_asesor2=asesor.kd_asesor) as nia2,
        (SELECT asesor.nm_asesor from asesor WHERE mapping_paud_visitasi.kd_asesor2=asesor.kd_asesor) as nama2,
        (SELECT asesor.no_hp from asesor WHERE mapping_paud_visitasi.kd_asesor2=asesor.kd_asesor) as hp2,
        (SELECT kab_kota.nm_kota from asesor LEFT JOIN kab_kota ON asesor.kota_id=kab_kota.kota_id
            WHERE mapping_paud_visitasi.kd_asesor2=asesor.kd_asesor) as kota2, mapping_paud_visitasi.tahun_akreditasi
            FROM mapping_paud_visitasi
        LEFT JOIN sekolah ON mapping_paud_visitasi.sekolah_id=sekolah.sekolah_id
        LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
        LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
        WHERE (kd_asesor1='$kd_user' OR kd_asesor2='$kd_user')
        AND mapping_paud_visitasi.tahun_akreditasi='$nama_tahun'
        AND mapping_paud_visitasi.provinsi_id='$provinsi_id'  ";
$result = $connect->query($sql);
if($result->num_rows > 0) {
while($row = $result->fetch_assoc()) {
$nomor++;
$asesor1 = $row['kd_asesor1'];
$asesor2 = $row['kd_asesor2'];
$file_pakta_integritas_1          = $row['file_pakta_integritas_1'];
$file_pakta_integritas_2          = $row['file_pakta_integritas_2'];
$file_berita_acara_visitasi       = $row['file_berita_acara_visitasi'];
$file_temuan_hasil_visitasi       = $row['file_temuan_hasil_visitasi'];
$file_absen_pembuka               = $row['file_absen_pembuka'];
$file_absen_penutup               = $row['file_absen_penutup'];
$file_foto_visitasi               = $row['file_foto_visitasi'];
$file_laporan_individu_1          = $row['file_laporan_individu_1'];
$file_laporan_individu_2          = $row['file_laporan_individu_2'];
$file_laporan_kelompok            = $row['file_laporan_kelompok'];
$file_penjelasan_hasil_akreditasi = $row['file_penjelasan_hasil_akreditasi'];

jika menurut anda query sql tersebut belum maksimal, silahkan diperbaiki agar maksimal kinerjanya

sampai disini apakah anda mengerti dengan apa yang saya maksudkan?
jika ada pertanyaan yang ingin ditanyakan dipersilahkan

=================================================================================================================

mantap kawanku yang cerdas, its work, sekarang kita lanjut untuk menampilkan modal "Unggah File" yang dimana jika tombol tersebut di-klik akan menampilkan modal yang berisi form untuk input file "Hasil KPA" berupa file PDF, file yang diunggah akan tersimpan pada direktori "../../../../simak/files/upload_file_hasil_kpa/"

berikut contoh kode sumber php untuk tanggal dan jam
<?php 
  date_default_timezone_set('Asia/Singapore');
  $tanggal= mktime(date("m"),date("d"),date("Y"));
  $tglAwal = date("Y-m-d", $tanggal);
  $tglAkhir = date("Y-m-d", $tanggal);
  $tglsekarang = date("Y-m-d", $tanggal);
  $jam=date("H:i:s");
?>

berikut contoh form:
<input name="tgl_file_hasil_kpa" id="tgl_file_hasil_kpa" type="hidden" value="<?php echo $tglsekarang; ?>" />
<input name="jam_file_hasil_kpa" id="jam_file_hasil_kpa" type="hidden" value="<?php echo $jam; ?>" />
<label for="file_laporan_hasil_kpa">Upload File Laporan Hasil KPA</label><br>
<input type="file" id="file_laporan_hasil_kpa" name="file_laporan_hasil_kpa" >

berikut contoh kode sumber php yang bisa anda contoh 
<?php 
  $id_mapping            	= $_POST['id_mapping'];
  $file_laporan_hasil_kpa = htmlspecialchars(addslashes($_FILES['file_laporan_hasil_kpa']['name']));
  $tgl_file_hasil_kpa  = $_POST['tgl_file_hasil_kpa'];
  $jam_file_hasil_kpa  = $_POST['jam_file_hasil_kpa'];


    $sqlfile = "SELECT * FROM mapping_paud_kpa WHERE id_mapping = '$id_mapping' ";
    $resultfile = mysqli_query($conn, $sqlfile);
    $row = mysqli_fetch_array($resultfile);
    
    //hapus gambar
    $folder="../files/upload_file_hasil_kpa/$row[file_laporan_hasil_kpa]";
    unlink($folder);
    
    //input file
    $temp = explode('.', $_FILES['file_laporan_hasil_kpa']['name']);
    $nama_baru = round(microtime(true)) . '.' . end($temp);
    move_uploaded_file($_FILES['file_laporan_hasil_kpa']['tmp_name'], '../../../../simak/files/upload_file_hasil_kpa/'.$nama_baru);

    $sqlupdate = "UPDATE mapping_paud_kpa SET file_laporan_hasil_kpa ='$nama_baru', tgl_file_hasil_kpa ='$tgl_file_hasil_kpa', jam_file_hasil_kpa ='$jam_file_hasil_kpa'
    WHERE id_mapping='$id_mapping' ";
?>

proses upload file "file_laporan_hasil_kpa" terjadi tanpa refresh browser (tanpa reload halaman) dan terjadi secara real-time pada perubahan tombol di kolom "PREVIEW" menjadi "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_visitasi.file_laporan_hasil_kpa berhasil terunggah

sampai disini apakah anda mengerti dengan apa yang saya maksudkan?
jika ada pertanyaan yang ingin ditanyakan dipersilahkan

=====================================================================================================================

kita lanjut kawan, pada kolom "PREVIEW" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_kpa/ pada tab baru di browser, untuk nama file diambil dari field mapping_paud_visitasi.file_laporan_hasil_kpa

- "File Pakta Integritas 1" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_visitasi_paud/, untuk nama file diambil dari field mapping_paud_visitasi.file_laporan_hasil_kpa






