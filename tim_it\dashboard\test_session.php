<?php
/**
 * Test session dan koneksi database untuk debugging dashboard
 */

// Include koneksi database
require_once '../../koneksi.php';

// Include session checker
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Session & Database</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Test Session & Database Connection</h1>
    
    <h2>1. Session Information</h2>
    <table>
        <tr><th>Key</th><th>Value</th></tr>
        <?php foreach ($_SESSION as $key => $value): ?>
        <tr>
            <td><?php echo htmlspecialchars($key); ?></td>
            <td><?php echo htmlspecialchars(is_array($value) ? json_encode($value) : $value); ?></td>
        </tr>
        <?php endforeach; ?>
    </table>
    
    <h2>2. Database Connection Test</h2>
    <?php
    if ($conn) {
        echo "<p class='success'>✓ Database connection successful</p>";
        echo "<p><strong>Database:</strong> " . $conn->get_server_info() . "</p>";
    } else {
        echo "<p class='error'>✗ Database connection failed</p>";
    }
    ?>
    
    <h2>3. Provinsi ID Check</h2>
    <?php
    $provinsi_id = $_SESSION['provinsi_id'] ?? null;
    if ($provinsi_id) {
        echo "<p class='success'>✓ Provinsi ID found: $provinsi_id</p>";
        
        // Test query sederhana
        $query = "SELECT COUNT(*) as total FROM sekolah WHERE provinsi_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $provinsi_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        echo "<p><strong>Total sekolah di provinsi ini:</strong> " . $row['total'] . "</p>";
        
        if ($row['total'] > 0) {
            echo "<p class='success'>✓ Data sekolah ditemukan</p>";
            
            // Test sample data
            $query_sample = "SELECT s.sekolah_id, s.nm_sekolah, s.rumpun, j.nm_jenjang 
                            FROM sekolah s 
                            LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id 
                            WHERE s.provinsi_id = ? 
                            LIMIT 5";
            $stmt_sample = $conn->prepare($query_sample);
            $stmt_sample->bind_param("i", $provinsi_id);
            $stmt_sample->execute();
            $result_sample = $stmt_sample->get_result();
            
            echo "<h3>Sample Data Sekolah:</h3>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Nama Sekolah</th><th>Rumpun</th><th>Jenjang</th></tr>";
            while ($row = $result_sample->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . ($row['sekolah_id'] ?? 'NULL') . "</td>";
                echo "<td>" . ($row['nm_sekolah'] ?? 'NULL') . "</td>";
                echo "<td>" . ($row['rumpun'] ?? 'NULL') . "</td>";
                echo "<td>" . ($row['nm_jenjang'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>✗ Tidak ada data sekolah di provinsi ini</p>";
        }
    } else {
        echo "<p class='error'>✗ Provinsi ID tidak ditemukan dalam session</p>";
    }
    ?>
    
    <h2>4. Test Dashboard API</h2>
    <button onclick="testDashboardAPI()">Test Dashboard API</button>
    <div id="api-result"></div>
    
    <script>
    function testDashboardAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.innerHTML = '<p>Loading...</p>';
        
        fetch('ajax/get_dashboard_data.php')
            .then(response => response.json())
            .then(data => {
                resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                resultDiv.innerHTML = '<p class="error">Error: ' + error.message + '</p>';
            });
    }
    </script>
    
    <h2>5. Quick Actions</h2>
    <p>
        <a href="debug_data.php">→ Debug Data</a> |
        <a href="test_connection.php">→ Test Connection</a> |
        <a href="dashboard.php">→ Dashboard</a>
    </p>
    
</body>
</html>
