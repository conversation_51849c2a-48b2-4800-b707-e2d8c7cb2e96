<?php
/**
 * AJAX handler untuk update status todo
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    $todo_id = intval($_POST['todo_id'] ?? 0);
    $status = trim($_POST['status'] ?? '');
    $user_id = $_SESSION['kd_user'];
    
    // Validasi input
    if ($todo_id <= 0) {
        throw new Exception('ID todo tidak valid');
    }
    
    if (!in_array($status, ['pending', 'completed'])) {
        throw new Exception('Status tidak valid');
    }
    
    // Untuk sementara hanya return success
    // Jika ada tabel todo_list, gunakan query ini:
    /*
    $query = "UPDATE todo_list 
              SET status = ?, updated_at = NOW() 
              WHERE id = ? AND user_id = ?";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("sis", $status, $todo_id, $user_id);
    
    if (!$stmt->execute()) {
        throw new Exception('Gagal mengupdate status todo');
    }
    
    if ($stmt->affected_rows === 0) {
        throw new Exception('Todo tidak ditemukan atau tidak ada perubahan');
    }
    */
    
    // Response
    echo json_encode([
        'success' => true,
        'message' => 'Status todo berhasil diupdate'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    error_log("Error in update_todo_status.php: " . $e->getMessage());
}

$conn->close();
?>
