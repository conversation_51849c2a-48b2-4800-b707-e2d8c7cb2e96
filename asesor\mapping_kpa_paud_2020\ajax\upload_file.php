<?php
/**
 * AJAX handler untuk upload file hasil KPA
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Asesor', '../../../login.php');

header('Content-Type: application/json');

// Set timezone
date_default_timezone_set('Asia/Singapore');

try {
    // Validasi request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Validasi input
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID Mapping tidak valid');
    }
    
    if (!isset($_FILES['file_laporan_hasil_kpa']) || $_FILES['file_laporan_hasil_kpa']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File tidak valid atau gagal diupload');
    }
    
    // Get data from POST
    $id_mapping = mysqli_real_escape_string($conn, $_POST['id_mapping']);
    $file_laporan_hasil_kpa = htmlspecialchars(addslashes($_FILES['file_laporan_hasil_kpa']['name']));
    $tgl_file_hasil_kpa = mysqli_real_escape_string($conn, $_POST['tgl_file_hasil_kpa']);
    $jam_file_hasil_kpa = mysqli_real_escape_string($conn, $_POST['jam_file_hasil_kpa']);
    
    // Validasi file extension (optional - sesuai request tidak perlu validasi ketat)
    $allowed_extensions = ['pdf'];
    $file_extension = strtolower(pathinfo($_FILES['file_laporan_hasil_kpa']['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_extensions)) {
        throw new Exception('Format file harus PDF');
    }
    
    // Cek apakah mapping exists dan milik asesor yang login
    $kd_user = mysqli_real_escape_string($conn, $_SESSION['kd_user'] ?? '');
    $provinsi_id = mysqli_real_escape_string($conn, $_SESSION['provinsi_id'] ?? '');

    $check_query = "SELECT m.*, a.kd_asesor
                    FROM mapping_paud_kpa m
                    LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
                    WHERE m.id_mapping = '$id_mapping'
                        AND a.kd_asesor = '$kd_user'
                        AND m.provinsi_id = '$provinsi_id'";

    $check_result = mysqli_query($conn, $check_query);

    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        throw new Exception('Data mapping tidak ditemukan atau tidak memiliki akses');
    }

    $mapping_data = mysqli_fetch_assoc($check_result);
    
    // Pastikan direktori upload exists terlebih dahulu
    $upload_dir = "../../../simak/files/upload_file_hasil_kpa/";

    // Directory validation
    if (!is_dir($upload_dir) || !is_writable($upload_dir)) {
        throw new Exception('Direktori upload tidak tersedia atau tidak dapat ditulis');
    }

    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            throw new Exception('Gagal membuat direktori upload: ' . $upload_dir);
        }
    }

    if (!is_writable($upload_dir)) {
        throw new Exception('Direktori upload tidak dapat ditulis: ' . $upload_dir);
    }

    // Hapus file lama jika ada
    if (!empty($mapping_data['file_laporan_hasil_kpa'])) {
        $old_file_path = $upload_dir . $mapping_data['file_laporan_hasil_kpa'];
        if (file_exists($old_file_path)) {
            unlink($old_file_path);
        }
    }

    // Generate nama file baru
    $temp = explode('.', $_FILES['file_laporan_hasil_kpa']['name']);
    $nama_baru = round(microtime(true)) . '.' . end($temp);
    $nama_baru_escaped = mysqli_real_escape_string($conn, $nama_baru);

    // Upload file baru
    $upload_path = $upload_dir . $nama_baru;

    if (!move_uploaded_file($_FILES['file_laporan_hasil_kpa']['tmp_name'], $upload_path)) {
        throw new Exception('Gagal mengupload file ke server');
    }
    
    // Update database dengan query langsung
    $update_query = "UPDATE mapping_paud_kpa
                     SET file_laporan_hasil_kpa = '$nama_baru_escaped',
                         tgl_file_hasil_kpa = '$tgl_file_hasil_kpa',
                         jam_file_hasil_kpa = '$jam_file_hasil_kpa'
                     WHERE id_mapping = '$id_mapping'";

    $update_result = mysqli_query($conn, $update_query);

    if (!$update_result) {
        // Jika update database gagal, hapus file yang sudah diupload
        if (file_exists($upload_path)) {
            unlink($upload_path);
        }
        $db_error = mysqli_error($conn);
        throw new Exception('Gagal menyimpan data ke database: ' . $db_error);
    }

    $affected_rows = mysqli_affected_rows($conn);

    if ($affected_rows === 0) {
        throw new Exception('Tidak ada data yang diupdate, periksa ID mapping');
    }
    
    // Response sukses
    echo json_encode([
        'success' => true,
        'message' => 'File berhasil di-upload',
        'data' => [
            'id_mapping' => $id_mapping,
            'filename' => $nama_baru,
            'upload_date' => $tgl_file_hasil_kpa,
            'upload_time' => $jam_file_hasil_kpa
        ]
    ]);
    
} catch (Exception $e) {
    // Response error
    echo json_encode([
        'success' => false,
        'message' => 'File gagal di-upload, silahkan upload ulang',
        'error' => $e->getMessage()
    ]);
}
?>
