<?php
/**
 * AJAX handler untuk upload file hasil KPA
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Asesor', '../../../login.php');

header('Content-Type: application/json');

// Set timezone
date_default_timezone_set('Asia/Singapore');

try {
    // Validasi request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Validasi input
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID Mapping tidak valid');
    }
    
    if (!isset($_FILES['file_laporan_hasil_kpa']) || $_FILES['file_laporan_hasil_kpa']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File tidak valid atau gagal diupload');
    }
    
    // Get data from POST
    $id_mapping = (int)$_POST['id_mapping'];
    $file_laporan_hasil_kpa = htmlspecialchars(addslashes($_FILES['file_laporan_hasil_kpa']['name']));
    $tgl_file_hasil_kpa = $_POST['tgl_file_hasil_kpa'];
    $jam_file_hasil_kpa = $_POST['jam_file_hasil_kpa'];
    
    // Validasi file extension (optional - sesuai request tidak perlu validasi ketat)
    $allowed_extensions = ['pdf'];
    $file_extension = strtolower(pathinfo($_FILES['file_laporan_hasil_kpa']['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_extensions)) {
        throw new Exception('Format file harus PDF');
    }
    
    // Cek apakah mapping exists dan milik asesor yang login
    $kd_user = $_SESSION['kd_user'] ?? '';
    $provinsi_id = $_SESSION['provinsi_id'] ?? '';
    
    $check_query = "SELECT m.*, a.kd_asesor 
                    FROM mapping_paud_kpa m
                    LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
                    WHERE m.id_mapping = ? 
                        AND a.kd_asesor = ? 
                        AND m.provinsi_id = ?";
    
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("isi", $id_mapping, $kd_user, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping tidak ditemukan atau tidak memiliki akses');
    }
    
    $mapping_data = $check_result->fetch_assoc();
    
    // Success log
    error_log("Upload success - ID Mapping: " . $id_mapping . ", File: " . $_FILES['file_laporan_hasil_kpa']['name']);

    // Pastikan direktori upload exists terlebih dahulu
    $upload_dir = "../../../simak/files/upload_file_hasil_kpa/";
    $absolute_upload_dir = realpath(dirname(__FILE__) . '/' . $upload_dir);

    // Directory validation
    if (!is_dir($upload_dir) || !is_writable($upload_dir)) {
        error_log("Upload error - Directory issue: " . $upload_dir);
    }

    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            throw new Exception('Gagal membuat direktori upload: ' . $upload_dir);
        }
    }

    if (!is_writable($upload_dir)) {
        throw new Exception('Direktori upload tidak dapat ditulis: ' . $upload_dir);
    }

    // Hapus file lama jika ada
    if (!empty($mapping_data['file_laporan_hasil_kpa'])) {
        $old_file_path = $upload_dir . $mapping_data['file_laporan_hasil_kpa'];
        if (file_exists($old_file_path)) {
            unlink($old_file_path);
            error_log("Upload debug - Old file deleted: " . $old_file_path);
        }
    }

    // Generate nama file baru
    $temp = explode('.', $_FILES['file_laporan_hasil_kpa']['name']);
    $nama_baru = round(microtime(true)) . '.' . end($temp);

    error_log("Upload debug - New filename: " . $nama_baru);

    // Upload file baru
    $upload_path = $upload_dir . $nama_baru;

    error_log("Upload debug - Upload path: " . $upload_path);
    error_log("Upload debug - Temp file: " . $_FILES['file_laporan_hasil_kpa']['tmp_name']);

    if (!move_uploaded_file($_FILES['file_laporan_hasil_kpa']['tmp_name'], $upload_path)) {
        $error_msg = 'Gagal mengupload file ke server. Path: ' . $upload_path;
        error_log("Upload debug - " . $error_msg);
        throw new Exception($error_msg);
    }

    error_log("Upload debug - File uploaded successfully to: " . $upload_path);
    
    // Update database
    $update_query = "UPDATE mapping_paud_kpa
                     SET file_laporan_hasil_kpa = ?,
                         tgl_file_hasil_kpa = ?,
                         jam_file_hasil_kpa = ?
                     WHERE id_mapping = ?";

    error_log("Upload debug - Update query: " . $update_query);
    error_log("Upload debug - Parameters: " . $nama_baru . ", " . $tgl_file_hasil_kpa . ", " . $jam_file_hasil_kpa . ", " . $id_mapping);

    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("sssi", $nama_baru, $tgl_file_hasil_kpa, $jam_file_hasil_kpa, $id_mapping);

    if (!$update_stmt->execute()) {
        // Jika update database gagal, hapus file yang sudah diupload
        if (file_exists($upload_path)) {
            unlink($upload_path);
        }
        $db_error = $conn->error;
        error_log("Upload debug - Database error: " . $db_error);
        throw new Exception('Gagal menyimpan data ke database: ' . $db_error);
    }

    $affected_rows = $update_stmt->affected_rows;
    error_log("Upload debug - Affected rows: " . $affected_rows);

    if ($affected_rows === 0) {
        error_log("Upload debug - No rows updated, possibly wrong ID mapping");
    }
    
    // Response sukses
    echo json_encode([
        'success' => true,
        'message' => 'File berhasil di-upload',
        'data' => [
            'id_mapping' => $id_mapping,
            'filename' => $nama_baru,
            'upload_date' => $tgl_file_hasil_kpa,
            'upload_time' => $jam_file_hasil_kpa
        ]
    ]);
    
} catch (Exception $e) {
    // Log error
    error_log("Upload error: " . $e->getMessage());

    // Response error
    echo json_encode([
        'success' => false,
        'message' => 'File gagal di-upload, silahkan upload ulang',
        'error' => $e->getMessage(),
        'debug_info' => [
            'upload_dir' => $upload_dir ?? 'not set',
            'id_mapping' => $id_mapping ?? 'not set',
            'file_info' => $_FILES['file_laporan_hasil_kpa'] ?? 'not set'
        ]
    ]);
}
?>
