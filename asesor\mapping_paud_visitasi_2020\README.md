# Modul Mapping Visitasi PAUD 2020

## 📋 **OVERVIEW**

Modul ini menampilkan data mapping visitasi PAUD untuk asesor yang sedang login. Asesor dapat melihat sekolah-sekolah PAUD yang telah di-assign untuk proses visitasi akreditasi dan mengelola dokumen-dokumen terkait.

## 📁 **STRUKTUR FILE**

```
asesor/mapping_paud_visitasi_2020/
├── mapping_paud_visitasi.php    # Main module file
├── preview_file.php             # Secure file preview handler
├── ajax/
│   ├── upload_file.php          # File upload handler
│   └── save_tanggal.php         # Save tanggal visitasi handler
├── js/
│   └── mapping_visitasi.js      # JavaScript functionality
└── README.md                    # This documentation
```

## 🎯 **FITUR UTAMA**

### **1. Tabel Data Mapping (5 Kolom)**
- **NO**: Nomor urut auto increment
- **SEKOLAH**: Informasi lengkap sekolah (NPSN, Nama, Jenjang, Kab/<PERSON>, <PERSON>pse<PERSON>, HP, <PERSON><PERSON><PERSON>)
- **ASESOR VISITASI**: Informasi Asesor A & B (NIA, Nama, Kab/Kota)
- **FORM UPLOAD DOKUMEN**: Tombol upload conditional berdasarkan asesor
- **DOKUMEN UNGGAHAN**: Status 11 jenis file dengan badge hijau/merah
- **JADWAL DAN AKSI**: Tanggal visitasi dan action buttons

### **2. Upload System (11 Jenis File)**
- **Asesor A Files (9)**: Pakta A, Berita Acara, Temuan, Absen (2), Foto, Laporan A, Laporan Kelompok, PHA
- **Asesor B Files (2)**: Pakta B, Laporan B
- **Individual Modals**: Setiap jenis file memiliki modal upload sendiri
- **Progress Bar**: Real-time upload progress tracking

### **3. File Management**
- **Upload Directory**: `../../../simak/files/upload_file_hasil_visitasi_paud/`
- **File Type**: PDF only (including foto visitasi)
- **Security**: Access control berdasarkan asesor ownership
- **Preview**: Click "Sudah Upload" untuk preview file

### **4. Tanggal Visitasi**
- **Input Tanggal**: Modal untuk input tanggal mulai visitasi
- **Format**: dd-mm-yyyy
- **Access**: Hanya Asesor A yang bisa input tanggal

## 🔍 **DATABASE QUERY**

### **Main Query (Corrected with Actual Table Structure):**
```sql
SELECT
    mv.id_mapping, mv.tahap, mv.tgl_mulai_visitasi, mv.tgl_akhir_visitasi,
    mv.no_surat, mv.tgl_surat,
    s.sekolah_id, s.npsn, s.nama_sekolah, s.nama_kepsek, s.no_hp_kepsek,
    j.nm_jenjang, kk.nm_kota,
    mv.kd_asesor1, mv.kd_asesor2,
    a1.nia1, a1.nm_asesor1, kk1.nm_kota as kota1,
    a2.nia2, a2.nm_asesor2, kk2.nm_kota as kota2,
    mv.file_pakta_integritas_1, mv.file_pakta_integritas_2,
    mv.file_berita_acara_visitasi, mv.file_temuan_hasil_visitasi,
    mv.file_absen_pembuka, mv.file_absen_penutup, mv.file_foto_visitasi,
    mv.tgl_file_foto_visitasi, mv.jam_file_foto_visitasi,
    mv.file_laporan_individu_1, mv.file_laporan_individu_2,
    mv.file_laporan_kelompok, mv.file_penjelasan_hasil_akreditasi
FROM mapping_paud_visitasi mv
LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
LEFT JOIN kab_kota kk1 ON a1.kota_id1 = kk1.kota_id
LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
LEFT JOIN kab_kota kk2 ON a2.kota_id2 = kk2.kota_id
WHERE (mv.kd_asesor1 = ? OR mv.kd_asesor2 = ?)
    AND mv.tahun_akreditasi = ?
    AND mv.provinsi_id = ?
ORDER BY mv.id_mapping DESC
```

## 🎨 **UI COMPONENTS**

### **Upload Buttons (Conditional Display):**
- **Asesor A Only**: 9 tombol upload dengan warna berbeda
- **Asesor B Only**: 2 tombol upload
- **Color Coding**: Primary, Success, Info, Warning, Secondary, Dark

### **Status Badges:**
- **Sudah Upload**: Green badge dengan eye icon (clickable untuk preview)
- **Belum Upload**: Red badge
- **Compact Display**: Font size 11px untuk muat banyak info

### **Modals:**
- **Upload Modal**: Individual modal per file type dengan progress bar
- **Tanggal Modal**: Modal untuk input tanggal visitasi
- **Responsive**: Bootstrap modal dengan proper styling

## 🔧 **SESSION REQUIREMENTS**

### **Required Session Variables:**
- `$_SESSION['kd_user']` - Kode asesor yang login
- `$_SESSION['provinsi_id']` - ID provinsi asesor
- `$_SESSION['level']` - Harus 'Asesor'

### **Access Control:**
- **Asesor A**: Full access (9 file uploads + tanggal input)
- **Asesor B**: Limited access (2 file uploads only)
- **Data Filter**: Hanya data mapping yang melibatkan asesor login

## 📱 **JavaScript Functionality**

### **Upload Functions:**
- `uploadFile(idMapping, fileType)` - Show upload modal dengan dynamic title
- `handleFileUpload()` - Process AJAX upload dengan progress bar
- `previewFile(filename)` - Preview file di tab baru

### **Tanggal Functions:**
- `inputTanggal(idMapping)` - Show tanggal input modal
- `saveTanggal()` - Save tanggal visitasi via AJAX

### **Utility Functions:**
- `showSuccess(message)` - Success notification dengan SweetAlert2
- `showError(message)` - Error notification
- `showInfo(message)` - Info notification

## 🚀 **FEATURES SUMMARY**

### **File Upload System:**
- ✅ **11 jenis file** dengan conditional access
- ✅ **Individual modals** per file type
- ✅ **Progress bar** real-time
- ✅ **Security validation** berdasarkan asesor role
- ✅ **File preview** dengan secure handler

### **Data Management:**
- ✅ **Optimized queries** dengan proper JOINs
- ✅ **No pagination** (tampil semua data)
- ✅ **Real-time updates** setelah upload
- ✅ **Responsive design** untuk mobile

### **User Experience:**
- ✅ **Role-based interface** (Asesor A vs B)
- ✅ **Visual indicators** dengan color-coded buttons
- ✅ **Intuitive workflow** dari upload sampai preview
- ✅ **Error handling** yang comprehensive

## 🔍 **TESTING**

### **Test Cases:**
1. **Asesor A Login**: Verify 9 upload buttons + tanggal input
2. **Asesor B Login**: Verify 2 upload buttons only
3. **File Upload**: Test each file type upload
4. **File Preview**: Test preview untuk uploaded files
5. **Tanggal Input**: Test tanggal visitasi input
6. **Access Control**: Test cross-asesor access prevention

### **Security Tests:**
- File ownership validation
- Path traversal prevention
- Session-based access control
- File type validation

---
**Version**: 1.1.0
**Status**: Production Ready ✅
**Features**: Complete Upload System + File Preview
**Database**: Updated dengan struktur tabel yang benar
**Tables**: asesor_1, asesor_2, mapping_paud_visitasi
**Files**: 11 file types dengan conditional access
**Last Updated**: 2024-12-19
