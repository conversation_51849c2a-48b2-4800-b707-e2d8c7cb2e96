# Dashboard SIMAK - Pivot Table Implementation Guide

## 🎯 **OVERVIEW**

Dashboard SIMAK sekarang menampilkan **sebaran sekolah per kabupaten/kota** dalam format **pivot table** yang terorganisir berdasarkan:
- **Rumpun Dasmen**: SD, SMP, SMA, SMK, MI, MTs, MA
- **Rumpun PAUD**: TK, KB, TPA, SPS, RA

## 📊 **PIVOT TABLE FORMAT**

### **Structure:**
- **Rows (Baris)**: Kabupaten/Kota di Kalimantan Timur
- **Columns (Kolom)**: Jenjang pendidikan per rumpun
- **Cells (Sel)**: Jumlah sekolah per kombinasi kab/kota + jenjang
- **Totals**: Per baris (kab/kota) dan per kolom (jenjang)
- **Grand Total**: Total keseluruhan di pojok kanan bawah

### **Example Dasmen Pivot:**
```
┌─────────────┬────┬─────┬─────┬─────┬───────┐
│ Kab/Kota    │ SD │ SMP │ SMA │ SMK │ Total │
├─────────────┼────┼─────┼─────┼─────┼───────┤
│ Samarinda   │ 45 │ 28  │ 22  │ 18  │ 113   │
│ Balikpapan  │ 38 │ 25  │ 20  │ 15  │ 98    │
│ Kutai Karta │ 25 │ 18  │ 12  │ 8   │ 63    │
│ Bontang     │ 12 │ 8   │ 6   │ 4   │ 30    │
├─────────────┼────┼─────┼─────┼─────┼───────┤
│ TOTAL       │120 │ 79  │ 60  │ 45  │ 304   │
└─────────────┴────┴─────┴─────┴─────┴───────┘
```

### **Example PAUD Pivot:**
```
┌─────────────┬────┬────┬─────┬───────┐
│ Kab/Kota    │ TK │ KB │ TPA │ Total │
├─────────────┼────┼────┼─────┼───────┤
│ Samarinda   │ 23 │ 8  │ 3   │ 34    │
│ Balikpapan  │ 18 │ 6  │ 2   │ 26    │
│ Kutai Karta │ 15 │ 5  │ 2   │ 22    │
│ Bontang     │ 7  │ 3  │ 1   │ 11    │
├─────────────┼────┼────┼─────┼───────┤
│ TOTAL       │ 63 │ 22 │ 8   │ 93    │
└─────────────┴────┴────┴─────┴───────┘
```

## 🔧 **TECHNICAL IMPLEMENTATION**

### **Database Queries:**
```sql
-- Query Dasmen Pivot
SELECT kk.nm_kota, j.nm_jenjang, COUNT(s.sekolah_id) as total
FROM sekolah s
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
WHERE s.provinsi_id = ? AND s.rumpun = 'dasmen'
GROUP BY kk.kota_id, kk.nm_kota, j.jenjang_id, j.nm_jenjang
ORDER BY kk.nm_kota, j.jenjang_id

-- Query PAUD Pivot
SELECT kk.nm_kota, j.nm_jenjang, COUNT(s.sekolah_id) as total
FROM sekolah s
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
WHERE s.provinsi_id = ? AND s.rumpun = 'paud'
GROUP BY kk.kota_id, kk.nm_kota, j.jenjang_id, j.nm_jenjang
ORDER BY kk.nm_kota, j.jenjang_id
```

### **API Response:**
```json
{
    "success": true,
    "data": {
        "sebaran_dasmen_kota": [
            {"nm_kota": "Samarinda", "nm_jenjang": "SD", "total": "45"},
            {"nm_kota": "Samarinda", "nm_jenjang": "SMP", "total": "28"},
            {"nm_kota": "Balikpapan", "nm_jenjang": "SD", "total": "38"}
        ],
        "sebaran_paud_kota": [
            {"nm_kota": "Samarinda", "nm_jenjang": "TK", "total": "23"},
            {"nm_kota": "Samarinda", "nm_jenjang": "KB", "total": "8"}
        ]
    }
}
```

### **JavaScript Processing:**
```javascript
// Group data by kota
const kotaData = {};
dashboardData.sebaran_dasmen_kota.forEach(item => {
    if (!kotaData[item.nm_kota]) {
        kotaData[item.nm_kota] = {};
    }
    kotaData[item.nm_kota][item.nm_jenjang] = parseInt(item.total);
});

// Generate pivot table HTML
Object.entries(kotaData).forEach(([kota, jenjangData]) => {
    // Create row for each kota
    // Calculate totals per row and column
});
```

## 🎨 **UI/UX FEATURES**

### **Responsive Design:**
- **Horizontal Scroll**: Table dapat di-scroll horizontal untuk mobile
- **Compact Layout**: Font size 11px untuk muat lebih banyak data
- **Bootstrap Integration**: Menggunakan Bootstrap table classes

### **Visual Styling:**
```css
/* Compact table styling */
font-size: 11px;
max-height: 220px; 
overflow-y: auto;

/* Cell styling */
border: 1px solid rgba(255,255,255,0.3);
padding: 4px;
text-align: center;

/* Header styling */
background: rgba(255,255,255,0.1);

/* Total row/column styling */
background: rgba(255,255,255,0.2);

/* Grand total styling */
background: rgba(255,255,255,0.3);
```

### **Color Coding:**
- **Empty Cells**: Ditampilkan sebagai "-"
- **Data Cells**: Background rgba(255,255,255,0.1)
- **Header**: Background rgba(255,255,255,0.1)
- **Totals**: Background rgba(255,255,255,0.2)
- **Grand Total**: Background rgba(255,255,255,0.3)

## 🔍 **TESTING & VERIFICATION**

### **Testing Sequence:**
1. **`test_sebaran.php`** ✅ - Preview pivot tables dengan data real
2. **`test_pivot_final.php`** 🆕 - Complete integration test
3. **`dashboard.php`** 🎯 - Final dashboard dengan pivot tables

### **Verification Checklist:**
- [ ] API returns `sebaran_dasmen_kota` array
- [ ] API returns `sebaran_paud_kota` array
- [ ] Tab navigation works (Dasmen ↔ PAUD)
- [ ] Pivot table displays correctly
- [ ] Horizontal scroll works on mobile
- [ ] Totals calculate correctly
- [ ] Empty cells show as "-"
- [ ] Grand total displays in bottom-right

## 📱 **MOBILE RESPONSIVENESS**

### **Mobile Optimizations:**
- **Horizontal Scroll**: Table container dengan `overflow-x: auto`
- **Touch-friendly**: Tab navigation dengan touch support
- **Compact Display**: Reduced font size dan padding
- **Readable Text**: Proper contrast untuk readability

### **Breakpoint Behavior:**
- **Desktop (>992px)**: Full table display
- **Tablet (768-992px)**: Horizontal scroll jika perlu
- **Mobile (<768px)**: Compact view dengan scroll

## 🚀 **PERFORMANCE CONSIDERATIONS**

### **Optimizations:**
- **Efficient Queries**: JOIN hanya tabel yang diperlukan
- **Grouped Data**: GROUP BY untuk mengurangi rows
- **Client-side Processing**: Pivot logic di JavaScript
- **Lazy Loading**: Data dimuat saat tab diklik

### **Memory Management:**
- **Destroy/Recreate**: Chart instances di-destroy sebelum update
- **Event Cleanup**: Proper event listener management
- **DOM Optimization**: Minimal DOM manipulation

## 🔧 **MAINTENANCE & UPDATES**

### **Regular Maintenance:**
1. Monitor query performance
2. Check data accuracy
3. Verify responsive behavior
4. Update styling jika diperlukan

### **Future Enhancements:**
- **Export to Excel**: Pivot table export functionality
- **Drill-down**: Click cell untuk detail
- **Filtering**: Filter by specific kab/kota atau jenjang
- **Sorting**: Sort by total atau nama

## 📋 **TROUBLESHOOTING**

### **Common Issues:**
1. **Empty Pivot**: Check if `rumpun` field has correct values
2. **Missing Kota**: Verify `kab_kota` table relationships
3. **Wrong Totals**: Check GROUP BY logic
4. **Layout Issues**: Verify CSS responsive classes

### **Debug Steps:**
1. Run `test_sebaran.php` untuk data verification
2. Check API response di browser console
3. Verify database relationships
4. Test responsive behavior

---
**Implementation**: Complete ✅  
**Status**: Production Ready 🚀  
**Last Updated**: 2024-12-19
