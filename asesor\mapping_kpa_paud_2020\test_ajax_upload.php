<?php
/**
 * Test AJAX upload functionality directly
 */

require_once '../../koneksi.php';
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

// Get sample mapping ID
$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

$query = "SELECT m.id_mapping, s.nama_sekolah
          FROM mapping_paud_kpa m
          LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
          LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
          WHERE a.kd_asesor = ? AND m.provinsi_id = ?
          LIMIT 1";

$stmt = $conn->prepare($query);
$stmt->bind_param("si", $kd_user, $provinsi_id);
$stmt->execute();
$result = $stmt->get_result();
$sample_mapping = $result->fetch_assoc();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX Upload</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 15px 0; }
        .progress { width: 100%; height: 25px; background: #f0f0f0; border-radius: 5px; overflow: hidden; margin: 10px 0; }
        .progress-bar { height: 100%; background: #007bff; transition: width 0.3s; text-align: center; line-height: 25px; color: white; }
        button { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin: 15px 0; padding: 10px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h2>Test AJAX Upload</h2>
    
    <?php if ($sample_mapping): ?>
        <p><strong>Sample Mapping ID:</strong> <?php echo $sample_mapping['id_mapping']; ?></p>
        <p><strong>Sekolah:</strong> <?php echo $sample_mapping['nama_sekolah']; ?></p>
        
        <form id="testUploadForm" enctype="multipart/form-data">
            <?php 
            date_default_timezone_set('Asia/Singapore');
            $tanggal = mktime(date("m"),date("d"),date("Y"));
            $tglsekarang = date("Y-m-d", $tanggal);
            $jam = date("H:i:s");
            ?>
            
            <input type="hidden" name="id_mapping" value="<?php echo $sample_mapping['id_mapping']; ?>">
            <input type="hidden" name="tgl_file_hasil_kpa" value="<?php echo $tglsekarang; ?>">
            <input type="hidden" name="jam_file_hasil_kpa" value="<?php echo $jam; ?>">
            
            <div class="form-group">
                <label>Upload File PDF:</label><br>
                <input type="file" name="file_laporan_hasil_kpa" accept=".pdf" required>
            </div>
            
            <div class="progress" id="uploadProgress" style="display: none;">
                <div class="progress-bar" id="progressBar" style="width: 0%">
                    <span id="progressText">0%</span>
                </div>
            </div>
            
            <button type="submit" id="uploadBtn">Test Upload</button>
        </form>
        
        <div id="result"></div>
        
    <?php else: ?>
        <p style="color: red;">No mapping data found for testing.</p>
    <?php endif; ?>
    
    <hr>
    <p><a href="mapping_kpa.php">← Back to Mapping KPA</a></p>
    
    <script>
    $(document).ready(function() {
        $('#testUploadForm').on('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const uploadBtn = $('#uploadBtn');
            const progressContainer = $('#uploadProgress');
            const progressBar = $('#progressBar');
            const progressText = $('#progressText');
            const resultDiv = $('#result');
            
            // Show progress
            progressContainer.show();
            progressBar.css('width', '0%');
            progressText.text('0%');
            uploadBtn.prop('disabled', true).text('Uploading...');
            resultDiv.html('');
            
            console.log('Starting AJAX upload test...');
            console.log('Form data:', formData);
            
            $.ajax({
                url: 'ajax/upload_file.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            const percentComplete = Math.round((e.loaded / e.total) * 100);
                            progressBar.css('width', percentComplete + '%');
                            progressText.text(percentComplete + '%');
                        }
                    }, false);
                    return xhr;
                },
                success: function(response) {
                    console.log('Upload response:', response);
                    
                    if (response.success) {
                        progressBar.css('background', '#28a745');
                        progressText.text('Success!');
                        resultDiv.html('<div class="result success">✅ ' + response.message + '</div>');
                    } else {
                        progressBar.css('background', '#dc3545');
                        progressText.text('Failed!');
                        resultDiv.html('<div class="result error">❌ ' + response.message + '<br>Error: ' + (response.error || 'Unknown') + '</div>');
                    }
                    
                    uploadBtn.prop('disabled', false).text('Test Upload');
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', xhr.responseText);
                    
                    progressBar.css('background', '#dc3545');
                    progressText.text('Error!');
                    resultDiv.html('<div class="result error">❌ AJAX Error: ' + error + '<br>Response: ' + xhr.responseText + '</div>');
                    
                    uploadBtn.prop('disabled', false).text('Test Upload');
                }
            });
        });
    });
    </script>
</body>
</html>
