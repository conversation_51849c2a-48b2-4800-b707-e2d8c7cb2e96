<?php
/**
 * Test final untuk pivot table dashboard
 */

// Include koneksi database
require_once '../../koneksi.php';

// Include session checker
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Final Pivot Table Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9; }
        .result { background-color: #fff; padding: 10px; margin: 10px 0; border-radius: 3px; border-left: 4px solid #007bff; }
        .status-ok { border-left-color: #28a745; }
        .status-error { border-left-color: #dc3545; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 3px; }
        button:hover { background: #0056b3; }
        .dashboard-preview { border: 2px solid #007bff; border-radius: 8px; padding: 15px; margin: 10px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
    </style>
</head>
<body>
    <h1>🎯 Test Final Pivot Table Dashboard</h1>
    
    <div class="box">
        <h2>✅ 1. Verifikasi API Response</h2>
        <button onclick="testPivotAPI()">Test Pivot API</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="box">
        <h2>📊 2. Preview Dashboard Pivot</h2>
        <button onclick="previewDashboard()">Preview Pivot Tables</button>
        <div id="preview-result" class="result"></div>
    </div>
    
    <div class="box">
        <h2>🔧 3. Test Tab Functionality</h2>
        <button onclick="testTabSwitching()">Test Tab Switching</button>
        <div id="tab-result" class="result"></div>
    </div>
    
    <div class="box">
        <h2>🎉 4. Final Status</h2>
        <div id="final-status" class="result">
            <p class="info">Jalankan semua test di atas untuk melihat status final</p>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.0/js/bootstrap.min.js"></script>
    
    <script>
    let testResults = {
        api: false,
        preview: false,
        tabs: false
    };
    
    function updateFinalStatus() {
        const allPassed = Object.values(testResults).every(result => result === true);
        const finalDiv = document.getElementById('final-status');
        
        if (allPassed) {
            finalDiv.className = 'result status-ok';
            finalDiv.innerHTML = `
                <h3 class="success">🎉 SEMUA TEST BERHASIL!</h3>
                <p>Dashboard pivot table siap digunakan:</p>
                <div style="text-align: center; margin: 20px 0;">
                    <a href="dashboard.php" target="_blank" style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px; display: inline-block;">
                        🚀 BUKA DASHBOARD PIVOT
                    </a>
                </div>
                <p><strong>Fitur yang tersedia:</strong></p>
                <ul>
                    <li>✅ Pivot table Dasmen per kabupaten/kota per jenjang</li>
                    <li>✅ Pivot table PAUD per kabupaten/kota per jenjang</li>
                    <li>✅ Tab navigation untuk switching</li>
                    <li>✅ Responsive design dengan horizontal scroll</li>
                    <li>✅ Real-time data dari database</li>
                </ul>
            `;
        } else {
            const failedTests = Object.entries(testResults)
                .filter(([key, value]) => value === false)
                .map(([key, value]) => key);
            
            finalDiv.className = 'result status-error';
            finalDiv.innerHTML = `
                <p class="error">⚠️ ADA TEST YANG GAGAL</p>
                <p>Test yang gagal: ${failedTests.join(', ')}</p>
                <p>Perbaiki masalah di atas sebelum menggunakan dashboard.</p>
            `;
        }
    }
    
    function testPivotAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.innerHTML = '<p class="info">Testing Pivot API...</p>';
        
        fetch('ajax/get_dashboard_data.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = '<h4 class="success">✓ API Response Success!</h4>';
                    
                    // Check Dasmen pivot data
                    if (data.data.sebaran_dasmen_kota && data.data.sebaran_dasmen_kota.length > 0) {
                        html += `<p class="success">✓ Sebaran Dasmen: ${data.data.sebaran_dasmen_kota.length} records</p>`;
                        
                        // Show sample data
                        const kotaList = [...new Set(data.data.sebaran_dasmen_kota.map(item => item.nm_kota))];
                        const jenjangList = [...new Set(data.data.sebaran_dasmen_kota.map(item => item.nm_jenjang))];
                        
                        html += `<p>Kab/Kota: ${kotaList.length} (${kotaList.slice(0, 3).join(', ')}...)</p>`;
                        html += `<p>Jenjang: ${jenjangList.length} (${jenjangList.join(', ')})</p>`;
                    } else {
                        html += '<p class="error">✗ No Sebaran Dasmen data</p>';
                    }
                    
                    // Check PAUD pivot data
                    if (data.data.sebaran_paud_kota && data.data.sebaran_paud_kota.length > 0) {
                        html += `<p class="success">✓ Sebaran PAUD: ${data.data.sebaran_paud_kota.length} records</p>`;
                        
                        // Show sample data
                        const paudKotaList = [...new Set(data.data.sebaran_paud_kota.map(item => item.nm_kota))];
                        const paudJenjangList = [...new Set(data.data.sebaran_paud_kota.map(item => item.nm_jenjang))];
                        
                        html += `<p>Kab/Kota: ${paudKotaList.length} (${paudKotaList.slice(0, 3).join(', ')}...)</p>`;
                        html += `<p>Jenjang: ${paudJenjangList.length} (${paudJenjangList.join(', ')})</p>`;
                    } else {
                        html += '<p class="error">✗ No Sebaran PAUD data</p>';
                    }
                    
                    testResults.api = (data.data.sebaran_dasmen_kota && data.data.sebaran_dasmen_kota.length > 0) ||
                                     (data.data.sebaran_paud_kota && data.data.sebaran_paud_kota.length > 0);
                    
                    resultDiv.className = testResults.api ? 'result status-ok' : 'result status-error';
                    resultDiv.innerHTML = html;
                } else {
                    testResults.api = false;
                    resultDiv.className = 'result status-error';
                    resultDiv.innerHTML = `<p class="error">✗ API Error: ${data.message}</p>`;
                }
                updateFinalStatus();
            })
            .catch(error => {
                testResults.api = false;
                resultDiv.className = 'result status-error';
                resultDiv.innerHTML = `<p class="error">✗ Fetch Error: ${error.message}</p>`;
                updateFinalStatus();
            });
    }
    
    function previewDashboard() {
        const resultDiv = document.getElementById('preview-result');
        resultDiv.innerHTML = '<p class="info">Generating preview...</p>';
        
        fetch('ajax/get_dashboard_data.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.sebaran_dasmen_kota) {
                    let html = '<div class="dashboard-preview">';
                    html += '<h4>🎯 Preview Dashboard Pivot Tables</h4>';
                    
                    // Preview Dasmen
                    html += '<h5>📊 Tab Dasmen Preview:</h5>';
                    html += '<div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin: 10px 0;">';
                    html += '<table style="width: 100%; font-size: 11px; color: white;">';
                    html += '<tr style="background: rgba(255,255,255,0.2);"><th style="padding: 4px; border: 1px solid rgba(255,255,255,0.3);">Kab/Kota</th>';
                    
                    // Get unique jenjang for header
                    const jenjangList = [...new Set(data.data.sebaran_dasmen_kota.map(item => item.nm_jenjang))];
                    jenjangList.forEach(jenjang => {
                        html += `<th style="padding: 4px; border: 1px solid rgba(255,255,255,0.3);">${jenjang}</th>`;
                    });
                    html += '<th style="padding: 4px; border: 1px solid rgba(255,255,255,0.3);">Total</th></tr>';
                    
                    // Group data by kota (show first 3 kota only for preview)
                    const kotaData = {};
                    data.data.sebaran_dasmen_kota.forEach(item => {
                        if (!kotaData[item.nm_kota]) kotaData[item.nm_kota] = {};
                        kotaData[item.nm_kota][item.nm_jenjang] = parseInt(item.total);
                    });
                    
                    const kotaList = Object.keys(kotaData).slice(0, 3);
                    kotaList.forEach(kota => {
                        html += '<tr><td style="padding: 4px; border: 1px solid rgba(255,255,255,0.3);"><strong>' + kota + '</strong></td>';
                        let kotaTotal = 0;
                        jenjangList.forEach(jenjang => {
                            const count = kotaData[kota][jenjang] || 0;
                            kotaTotal += count;
                            html += `<td style="padding: 4px; border: 1px solid rgba(255,255,255,0.3); text-align: center;">${count > 0 ? count : '-'}</td>`;
                        });
                        html += `<td style="padding: 4px; border: 1px solid rgba(255,255,255,0.3); text-align: center;"><strong>${kotaTotal}</strong></td></tr>`;
                    });
                    
                    html += '</table>';
                    html += '<p style="text-align: center; margin: 10px 0;"><em>... dan kota lainnya</em></p>';
                    html += '</div>';
                    
                    // Preview PAUD
                    if (data.data.sebaran_paud_kota && data.data.sebaran_paud_kota.length > 0) {
                        html += '<h5>👶 Tab PAUD Preview:</h5>';
                        html += '<p style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px;">Format yang sama dengan pivot table per kabupaten/kota per jenjang PAUD</p>';
                    }
                    
                    html += '</div>';
                    
                    testResults.preview = true;
                    resultDiv.className = 'result status-ok';
                    resultDiv.innerHTML = html;
                } else {
                    testResults.preview = false;
                    resultDiv.className = 'result status-error';
                    resultDiv.innerHTML = '<p class="error">✗ No data for preview</p>';
                }
                updateFinalStatus();
            })
            .catch(error => {
                testResults.preview = false;
                resultDiv.className = 'result status-error';
                resultDiv.innerHTML = `<p class="error">✗ Preview Error: ${error.message}</p>`;
                updateFinalStatus();
            });
    }
    
    function testTabSwitching() {
        const resultDiv = document.getElementById('tab-result');
        
        // Test if Bootstrap and jQuery are available
        if (typeof $ !== 'undefined' && typeof $.fn.tab !== 'undefined') {
            testResults.tabs = true;
            resultDiv.className = 'result status-ok';
            resultDiv.innerHTML = `
                <p class="success">✓ Tab functionality ready</p>
                <p>jQuery: Available</p>
                <p>Bootstrap: Available</p>
                <p>Tab switching akan berfungsi di dashboard</p>
            `;
        } else {
            testResults.tabs = false;
            resultDiv.className = 'result status-error';
            resultDiv.innerHTML = `
                <p class="error">✗ Tab functionality not ready</p>
                <p>jQuery: ${typeof $ !== 'undefined' ? 'Available' : 'Missing'}</p>
                <p>Bootstrap: ${typeof $.fn.tab !== 'undefined' ? 'Available' : 'Missing'}</p>
            `;
        }
        updateFinalStatus();
    }
    </script>
    
    <div class="box">
        <h2>📋 Quick Actions</h2>
        <p>
            <a href="dashboard.php" target="_blank" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;">🚀 Dashboard Utama</a>
            <a href="test_sebaran.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px; margin-left: 10px;">📊 Test Sebaran</a>
            <a href="ajax/get_dashboard_data.php" target="_blank" style="background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px; margin-left: 10px;">🔗 Direct API</a>
        </p>
    </div>
    
</body>
</html>
