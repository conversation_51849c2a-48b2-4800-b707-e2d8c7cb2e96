<?php
/**
 * Verify upload results
 */

require_once '../../koneksi.php';
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

echo "<h2>🔍 Verifikasi Upload Results</h2>";

// Check database for uploaded files
$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

$query = "SELECT 
            m.id_mapping, 
            s.nama_sekolah,
            m.file_laporan_hasil_kpa,
            m.tgl_file_hasil_kpa,
            m.jam_file_hasil_kpa
          FROM mapping_paud_kpa m
          LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
          LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
          WHERE a.kd_asesor = ? 
              AND m.provinsi_id = ?
              AND m.file_laporan_hasil_kpa IS NOT NULL 
              AND m.file_laporan_hasil_kpa != ''
          ORDER BY m.id_mapping DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param("si", $kd_user, $provinsi_id);
$stmt->execute();
$result = $stmt->get_result();

echo "<h3>📊 Database Records with Uploaded Files:</h3>";

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>ID Mapping</th>";
    echo "<th style='padding: 8px;'>Sekolah</th>";
    echo "<th style='padding: 8px;'>Filename</th>";
    echo "<th style='padding: 8px;'>Upload Date</th>";
    echo "<th style='padding: 8px;'>Upload Time</th>";
    echo "<th style='padding: 8px;'>File Exists</th>";
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        $file_path = "../../../simak/files/upload_file_hasil_kpa/" . $row['file_laporan_hasil_kpa'];
        $file_exists = file_exists($file_path);
        $file_size = $file_exists ? filesize($file_path) : 0;
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . $row['id_mapping'] . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['nama_sekolah']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['file_laporan_hasil_kpa']) . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['tgl_file_hasil_kpa'] ?: '-') . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['jam_file_hasil_kpa'] ?: '-') . "</td>";
        echo "<td style='padding: 8px;'>";
        if ($file_exists) {
            echo "<span style='color: green;'>✅ YES (" . number_format($file_size/1024, 1) . " KB)</span>";
        } else {
            echo "<span style='color: red;'>❌ NO</span>";
        }
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<p><strong>Total uploaded files:</strong> " . $result->num_rows . "</p>";
    
} else {
    echo "<p style='color: orange;'>⚠️ No uploaded files found in database</p>";
}

// Check upload directory
echo "<h3>📁 Upload Directory Contents:</h3>";
$upload_dir = "../../../simak/files/upload_file_hasil_kpa/";

if (is_dir($upload_dir)) {
    $files = scandir($upload_dir);
    $files = array_diff($files, array('.', '..'));
    
    if (!empty($files)) {
        echo "<ul>";
        foreach ($files as $file) {
            $file_path = $upload_dir . $file;
            $file_size = filesize($file_path);
            $file_time = date('Y-m-d H:i:s', filemtime($file_path));
            
            echo "<li>";
            echo "<strong>" . htmlspecialchars($file) . "</strong> ";
            echo "(" . number_format($file_size/1024, 1) . " KB) ";
            echo "<small>- Modified: " . $file_time . "</small>";
            echo "</li>";
        }
        echo "</ul>";
        
        echo "<p><strong>Total files in directory:</strong> " . count($files) . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Upload directory is empty</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Upload directory not found</p>";
}

// Test specific ID 5888
echo "<h3>🎯 Specific Check for ID 5888 (Test Upload):</h3>";
$check_query = "SELECT * FROM mapping_paud_kpa WHERE id_mapping = 5888";
$check_result = mysqli_query($conn, $check_query);

if ($check_result && mysqli_num_rows($check_result) > 0) {
    $check_row = mysqli_fetch_assoc($check_result);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th style='padding: 8px; background: #f0f0f0;'>Field</th><th style='padding: 8px; background: #f0f0f0;'>Value</th></tr>";
    echo "<tr><td style='padding: 8px;'>ID Mapping</td><td style='padding: 8px;'>" . $check_row['id_mapping'] . "</td></tr>";
    echo "<tr><td style='padding: 8px;'>File Name</td><td style='padding: 8px;'>" . ($check_row['file_laporan_hasil_kpa'] ?: 'EMPTY') . "</td></tr>";
    echo "<tr><td style='padding: 8px;'>Upload Date</td><td style='padding: 8px;'>" . ($check_row['tgl_file_hasil_kpa'] ?: 'EMPTY') . "</td></tr>";
    echo "<tr><td style='padding: 8px;'>Upload Time</td><td style='padding: 8px;'>" . ($check_row['jam_file_hasil_kpa'] ?: 'EMPTY') . "</td></tr>";
    echo "</table>";
    
    if (!empty($check_row['file_laporan_hasil_kpa'])) {
        $test_file_path = $upload_dir . $check_row['file_laporan_hasil_kpa'];
        if (file_exists($test_file_path)) {
            echo "<p style='color: green;'>✅ <strong>SUCCESS!</strong> File exists and database updated correctly!</p>";
        } else {
            echo "<p style='color: red;'>❌ Database updated but file not found in directory</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Database not updated</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ID Mapping 5888 not found</p>";
}

?>

<hr>
<p>
    <a href="mapping_kpa.php">← Back to Mapping KPA</a> | 
    <a href="test_ajax_upload.php">Test Upload Again</a>
</p>
