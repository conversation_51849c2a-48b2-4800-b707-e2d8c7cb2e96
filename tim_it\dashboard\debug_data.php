<?php
/**
 * Debug file untuk menganalisis struktur data yang sebenarnya
 */

// Include koneksi database
require_once '../../koneksi.php';

// Include session checker
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

echo "<h2>Debug Dashboard Data Structure</h2>";

try {
    $provinsi_id = $_SESSION['provinsi_id'];
    echo "<p><strong>Provinsi ID Session:</strong> $provinsi_id</p>";
    
    // Debug 1: Cek total sekolah tanpa filter
    echo "<h3>1. Total Sekolah (Tanpa Filter)</h3>";
    $query = "SELECT COUNT(*) as total FROM sekolah";
    $result = $conn->query($query);
    $row = $result->fetch_assoc();
    echo "<p>Total sekolah di database: " . $row['total'] . "</p>";
    
    // Debug 2: Cek sekolah berdasarkan provinsi
    echo "<h3>2. Sekolah per Provinsi</h3>";
    $query = "SELECT provinsi_id, COUNT(*) as total FROM sekolah GROUP BY provinsi_id ORDER BY total DESC LIMIT 10";
    $result = $conn->query($query);
    echo "<table border='1'>";
    echo "<tr><th>Provinsi ID</th><th>Total Sekolah</th></tr>";
    while ($row = $result->fetch_assoc()) {
        $highlight = ($row['provinsi_id'] == $provinsi_id) ? " style='background-color: yellow;'" : "";
        echo "<tr$highlight><td>" . $row['provinsi_id'] . "</td><td>" . $row['total'] . "</td></tr>";
    }
    echo "</table>";
    
    // Debug 3: Cek sekolah di provinsi user
    echo "<h3>3. Sekolah di Provinsi $provinsi_id</h3>";
    $query = "SELECT COUNT(*) as total FROM sekolah WHERE provinsi_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    echo "<p>Total sekolah di provinsi $provinsi_id: " . $row['total'] . "</p>";
    
    // Debug 4: Cek rumpun yang ada
    echo "<h3>4. Rumpun yang Ada di Database</h3>";
    $query = "SELECT rumpun, COUNT(*) as total FROM sekolah WHERE provinsi_id = ? GROUP BY rumpun";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    echo "<table border='1'>";
    echo "<tr><th>Rumpun</th><th>Total</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . ($row['rumpun'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
    }
    echo "</table>";
    
    // Debug 5: Cek status keaktifan
    echo "<h3>5. Status Keaktifan di Provinsi $provinsi_id</h3>";
    $query = "SELECT status_keaktifan_id, COUNT(*) as total FROM sekolah WHERE provinsi_id = ? GROUP BY status_keaktifan_id";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    echo "<table border='1'>";
    echo "<tr><th>Status Keaktifan ID</th><th>Total</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . ($row['status_keaktifan_id'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
    }
    echo "</table>";
    
    // Debug 6: Cek soft delete
    echo "<h3>6. Soft Delete Status di Provinsi $provinsi_id</h3>";
    $query = "SELECT soft_delete, COUNT(*) as total FROM sekolah WHERE provinsi_id = ? GROUP BY soft_delete";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    echo "<table border='1'>";
    echo "<tr><th>Soft Delete</th><th>Total</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . ($row['soft_delete'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
    }
    echo "</table>";
    
    // Debug 7: Cek jenjang yang ada
    echo "<h3>7. Jenjang yang Ada di Database</h3>";
    $query = "SELECT * FROM jenjang ORDER BY jenjang_id";
    $result = $conn->query($query);
    echo "<table border='1'>";
    echo "<tr><th>ID Jenjang</th><th>Jenjang ID</th><th>Nama Jenjang</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . $row['id_jenjang'] . "</td><td>" . $row['jenjang_id'] . "</td><td>" . $row['nm_jenjang'] . "</td></tr>";
    }
    echo "</table>";
    
    // Debug 8: Cek jenjang_id di sekolah
    echo "<h3>8. Jenjang ID di Sekolah Provinsi $provinsi_id</h3>";
    $query = "SELECT jenjang_id, COUNT(*) as total FROM sekolah WHERE provinsi_id = ? GROUP BY jenjang_id";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    echo "<table border='1'>";
    echo "<tr><th>Jenjang ID</th><th>Total</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . ($row['jenjang_id'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
    }
    echo "</table>";
    
    // Debug 9: Sample data sekolah
    echo "<h3>9. Sample Data Sekolah (5 record pertama)</h3>";
    $query = "SELECT sekolah_id, nama_sekolah, npsn, jenjang_id, rumpun, status_keaktifan_id, soft_delete 
              FROM sekolah WHERE provinsi_id = ? LIMIT 5";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Nama</th><th>NPSN</th><th>Jenjang ID</th><th>Rumpun</th><th>Status Aktif</th><th>Soft Delete</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['sekolah_id'] . "</td>";
        echo "<td>" . $row['nama_sekolah'] . "</td>";
        echo "<td>" . $row['npsn'] . "</td>";
        echo "<td>" . ($row['jenjang_id'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['rumpun'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['status_keaktifan_id'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['soft_delete'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Debug 10: Cek hasil akreditasi
    echo "<h3>10. Sample Hasil Akreditasi</h3>";
    $query = "SELECT COUNT(*) as total FROM hasil_akreditasi WHERE provinsi_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    echo "<p>Total hasil akreditasi di provinsi $provinsi_id: " . $row['total'] . "</p>";
    
    if ($row['total'] > 0) {
        $query = "SELECT ha.peringkat, COUNT(*) as total 
                  FROM hasil_akreditasi ha 
                  WHERE ha.provinsi_id = ? 
                  GROUP BY ha.peringkat";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $provinsi_id);
        $stmt->execute();
        $result = $stmt->get_result();
        echo "<table border='1'>";
        echo "<tr><th>Peringkat</th><th>Total</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr><td>" . ($row['peringkat'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
        }
        echo "</table>";
    }
    
    echo "<p><a href='dashboard.php'>← Kembali ke Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
}

$conn->close();
?>
