<?php
/**
 * AJAX handler untuk mengambil todo list
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    $provinsi_id = $_SESSION['provinsi_id'];
    $user_id = $_SESSION['kd_user'];
    
    // Query untuk mengambil todo list
    // Untuk sementara kita buat data sample, nanti bisa dibuat tabel todo_list
    $sample_todos = [
        [
            'id' => 1,
            'task' => 'Verifikasi data akreditasi SMA Negeri 1 Samarinda',
            'deadline' => '2 hari',
            'priority' => 'high',
            'status' => 'pending'
        ],
        [
            'id' => 2,
            'task' => 'Update data PAUD di Kutai Kartanegara',
            'deadline' => 'Selesai',
            'priority' => 'medium',
            'status' => 'completed'
        ],
        [
            'id' => 3,
            'task' => 'Buat laporan bulanan akreditasi',
            'deadline' => '5 hari',
            'priority' => 'medium',
            'status' => 'pending'
        ],
        [
            'id' => 4,
            'task' => 'Review mapping visitasi bulan ini',
            'deadline' => '1 minggu',
            'priority' => 'low',
            'status' => 'pending'
        ],
        [
            'id' => 5,
            'task' => 'Koordinasi dengan asesor untuk jadwal visitasi',
            'deadline' => '3 hari',
            'priority' => 'high',
            'status' => 'pending'
        ]
    ];
    
    // Jika ada tabel todo_list, gunakan query ini:
    /*
    $query = "SELECT id, task, deadline, priority, status 
              FROM todo_list 
              WHERE user_id = ? 
              AND (status != 'completed' OR DATE(updated_at) = CURDATE())
              ORDER BY 
                CASE priority 
                    WHEN 'high' THEN 1 
                    WHEN 'medium' THEN 2 
                    WHEN 'low' THEN 3 
                END,
                created_at DESC
              LIMIT 10";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $todos = [];
    while ($row = $result->fetch_assoc()) {
        $todos[] = $row;
    }
    */
    
    // Response
    echo json_encode([
        'success' => true,
        'data' => $sample_todos
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
    error_log("Error in get_todo_list.php: " . $e->getMessage());
}

$conn->close();
?>
