<?php
/**
 * Test sederhana untuk dashboard - KISS Method
 */

// Include koneksi database
require_once '../../koneksi.php';

// Include session checker
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

$provinsi_id = $_SESSION['provinsi_id'] ?? 64;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Dashboard Simple</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Test Dashboard Simple - KISS Method</h1>
    <p><strong>Provinsi ID:</strong> <?php echo $provinsi_id; ?></p>
    
    <h2>1. Test Query Dasmen</h2>
    <?php
    $query = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              WHERE s.provinsi_id = ? AND s.rumpun = 'dasmen'
              GROUP BY j.jenjang_id, j.nm_jenjang
              ORDER BY j.jenjang_id";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<table>";
    echo "<tr><th>Jenjang</th><th>Total</th></tr>";
    $total_dasmen = 0;
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . ($row['nm_jenjang'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
        $total_dasmen += $row['total'];
    }
    echo "</table>";
    echo "<p><strong>Total Dasmen:</strong> $total_dasmen</p>";
    ?>
    
    <h2>2. Test Query PAUD</h2>
    <?php
    $query = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              WHERE s.provinsi_id = ? AND s.rumpun = 'paud'
              GROUP BY j.jenjang_id, j.nm_jenjang
              ORDER BY j.jenjang_id";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<table>";
    echo "<tr><th>Jenjang</th><th>Total</th></tr>";
    $total_paud = 0;
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . ($row['nm_jenjang'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
        $total_paud += $row['total'];
    }
    echo "</table>";
    echo "<p><strong>Total PAUD:</strong> $total_paud</p>";
    ?>
    
    <h2>3. Test Query Kesetaraan</h2>
    <?php
    $query = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              WHERE s.provinsi_id = ? AND s.rumpun = 'kesetaraan'
              GROUP BY j.jenjang_id, j.nm_jenjang
              ORDER BY j.jenjang_id";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<table>";
    echo "<tr><th>Jenjang</th><th>Total</th></tr>";
    $total_kesetaraan = 0;
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . ($row['nm_jenjang'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
        $total_kesetaraan += $row['total'];
    }
    echo "</table>";
    echo "<p><strong>Total Kesetaraan:</strong> $total_kesetaraan</p>";
    ?>
    
    <h2>4. Test Query Akreditasi</h2>
    <?php
    $query = "SELECT s.rumpun, ha.peringkat, COUNT(*) as total
              FROM hasil_akreditasi ha
              JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
              WHERE ha.provinsi_id = ? AND ha.peringkat IN ('A', 'B', 'C')
              GROUP BY s.rumpun, ha.peringkat
              ORDER BY s.rumpun, ha.peringkat";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<table>";
    echo "<tr><th>Rumpun</th><th>Peringkat</th><th>Total</th></tr>";
    $has_akreditasi = false;
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . $row['rumpun'] . "</td><td>" . $row['peringkat'] . "</td><td>" . $row['total'] . "</td></tr>";
        $has_akreditasi = true;
    }
    if (!$has_akreditasi) {
        echo "<tr><td colspan='3'>Tidak ada data akreditasi</td></tr>";
    }
    echo "</table>";
    ?>
    
    <h2>5. Test API Dashboard</h2>
    <button onclick="testAPI()">Test API</button>
    <div id="api-result"></div>
    
    <script>
    function testAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.innerHTML = '<p>Loading...</p>';
        
        fetch('ajax/get_dashboard_data.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = '<h3>API Response Success!</h3>';
                    html += '<p><strong>Total Dasmen:</strong> ' + data.data.total_dasmen + '</p>';
                    html += '<p><strong>Total PAUD:</strong> ' + data.data.total_paud + '</p>';
                    html += '<p><strong>Total Kesetaraan:</strong> ' + data.data.total_kesetaraan + '</p>';
                    html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<p class="error">Error: ' + data.message + '</p>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<p class="error">Fetch Error: ' + error.message + '</p>';
            });
    }
    </script>
    
    <h2>6. Summary</h2>
    <p><strong>Total Sekolah:</strong> <?php echo $total_dasmen + $total_paud + $total_kesetaraan; ?></p>
    <p><strong>Status:</strong> 
        <?php if (($total_dasmen + $total_paud + $total_kesetaraan) > 0): ?>
            <span class="success">✓ Data sekolah ditemukan</span>
        <?php else: ?>
            <span class="error">✗ Tidak ada data sekolah</span>
        <?php endif; ?>
    </p>
    
    <p>
        <a href="dashboard.php">→ Dashboard</a> |
        <a href="debug_data.php">→ Debug Data</a>
    </p>
    
</body>
</html>
