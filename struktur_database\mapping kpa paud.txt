Berikut ini adalah struktur tabel "mapping_paud_kpa" :
id_mapping	int(11)
sekolah_id	int(11)
kd_asesor	varchar(25)
tgl_penetapan_kpa	date
tahap	int(11)
tahun_akreditasi	varchar(4)
file_laporan_hasil_kpa	varchar(50)
tgl_file_hasil_kpa	date
jam_file_hasil_kpa	time
provinsi_id	int(11)


Berikut ini struktur tabel "sekolah" :
sekolah_id int(11)
nama_sekolah varchar(100)
npsn varchar(30)
jenjang_id int(1)
rumpun varchar(20)
alamat text
tipe_sekolah_id int(1)
status_sekolah_id int(1)
provinsi_id int(2)
kota_id int(6)
desa_kel<PERSON>han varchar(200)
kecamatan varchar(100)
nama_kepsek varchar(100)
no_hp_kepsek varchar(50)
no_wa_kepsek varchar(50)
nama_operator varchar(50)
no_hp_operator varchar(20)
no_wa_operator varchar(20)
email varchar(100)
nama_ya<PERSON>an varchar(200)
no_akte varchar(50)
tahun_berdiri varchar(4)
status_keaktifan_id varchar(1)
soft_delete rchar(1)


Berikut ini adalah struktur tabel "jenjang" :
id_jenjang int(11)
jenjang_id varchar(2)
nm_jenjang varchar(15)


Berikut ini struktur tabel "kab_kota" :
id_kota	int(11)	
kota_id	varchar(10)
nm_kota	varchar(50)
provinsi_id	int(11)
kd_user	varchar(25)


Berikut ini struktur tabel "asesor" :
id_asesor int(11)
kd_asesor varchar(25)
nia varchar(20) 
nm_asesor	varchar(100) 
ktp	varchar(20) 
unit_kerja	varchar(300) 
kota_id varchar(10)
provinsi_id	int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan	varchar(50)
jabatan_struktural varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat	date
kegiatan varchar(50)
status_keaktifan_id	varchar(1)
sebab text
kd_user	varchar(50)
soft_delete	varchar(1)


Berikut ini struktur tabel "mapping_paud_kpa_tahun" :
id_mapping_tahun	int(11)
nama_tahun	int(4)
provinsi_id	int(11)

====================================================================================================================

buatlah modul "Mapping KPA" pada direktori asesor/mapping_kpa_paud_2020/mapping_kpa.php, juga tersedia sub direktori ajax dan js, tabel header yang digunakan adalah :

- NO;

- SEKOLAH, pada tabel body berisi data sebagai berikut : NPSN : sekolah.npsn, NAMA : sekolah.nama_sekolah, JENJANG : jenjang.jenjang_id=sekolah.jenjang_id (jenjang.nm_jenjang), KAB/KOTA : sekolah.kota_id = kab_kota.kota_id (kab_kota.nm_kota), NAMA KEPSEK : sekolah.nama_kepsek, NO HP KEPSEK : sekolah.no_hp_kepsek;

- ASESOR KPA, pada tabel body berisi NIA : asesor.nia, NAMA : asesor.nm_asesor, KAB/KOTA : asesor.kota_id = kab_kota.kota_id (kab_kota.nm_kota);

- DOKUMEN UNGGAHAN pada tabel body berisi label "File Hasil KPA :" dan tombol dengan nama "Unggah File" yang dimana jika tombol tersebut di-klik akan menampilkan modal yang berisi form untuk input file "Hasil KPA", untuk sementara tampilkan tombolnya saja terlebih dahulu);

- PREVIEW pada tabel body berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_kpa.file_laporan_hasil_kpa ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah;

- PELAKSANAAN KEGIATAN pada tabel body berisi "Tanggal Penetapan KPA PAUD : mapping_paud_kpa.tgl_penetapan_kpa;

- TAHAP pada tabel body berisi field tahap mapping_paud_kpa.tahap


oh iya lupa, tambahkan
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Asesor
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>

berikut di bawah ini query sql yang bisa anda gunakan untuk menampilkan data:

SELECT mapping_paud_kpa.id_mapping, mapping_paud_kpa.sekolah_id, sekolah.npsn, sekolah.nama_sekolah, sekolah.nama_kepsek, sekolah.no_hp_kepsek, sekolah.no_wa_kepsek, jenjang.nm_jenjang, kab_kota.nm_kota, sekolah.rumpun, mapping_paud_kpa.tahun_akreditasi, mapping_paud_kpa_tahun.nama_tahun, asesor.kd_asesor, asesor.nia, asesor.nm_asesor, asesor.no_hp, mapping_paud_kpa.tahap, mapping_paud_kpa.file_laporan_hasil_kpa, (SELECT kab_kota.nm_kota from asesor LEFT JOIN kab_kota ON asesor.kota_id=kab_kota.kota_id WHERE mapping_paud_kpa.kd_asesor=asesor.kd_asesor) as kota_asesor FROM mapping_paud_kpa 
LEFT JOIN sekolah ON mapping_paud_kpa.sekolah_id=sekolah.sekolah_id
LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
LEFT JOIN asesor ON mapping_paud_kpa.kd_asesor=asesor.kd_asesor
LEFT JOIN mapping_paud_kpa_tahun ON mapping_paud_kpa.tahun_akreditasi=mapping_paud_kpa_tahun.nama_tahun
WHERE mapping_paud_kpa.tahun_akreditasi = mapping_paud_kpa_tahun.nama_tahun
AND asesor.kd_asesor='$kd_user' AND sekolah.rumpun = 'paud' AND mapping_paud_kpa.provinsi_id = '$provinsi_id' (session login) AND mapping_paud_kpa_tahun.provinsi_id = '$provinsi_id' (session login)

jika menurut anda query sql tersebut belum maksimal, silahkan diperbaiki agar maksimal kinerjanya

sampai disini apakah anda mengerti dengan apa yang saya maksudkan?
jika ada pertanyaan yang ingin ditanyakan dipersilahkan

=====================================================================================================================

mantap kawanku yang cerdas, its work, sekarang kita lanjut untuk menampilkan modal "Unggah File" yang dimana jika tombol tersebut di-klik akan menampilkan modal yang berisi form untuk input file "Hasil KPA" berupa file PDF, file yang diunggah akan tersimpan pada direktori "../../../../simak/files/upload_file_hasil_kpa/"

berikut contoh kode sumber php untuk tanggal dan jam
<?php 
  date_default_timezone_set('Asia/Singapore');
  $tanggal= mktime(date("m"),date("d"),date("Y"));
  $tglAwal = date("Y-m-d", $tanggal);
  $tglAkhir = date("Y-m-d", $tanggal);
  $tglsekarang = date("Y-m-d", $tanggal);
  $jam=date("H:i:s");
?>

berikut contoh form:
<input name="tgl_file_hasil_kpa" id="tgl_file_hasil_kpa" type="hidden" value="<?php echo $tglsekarang; ?>" />
<input name="jam_file_hasil_kpa" id="jam_file_hasil_kpa" type="hidden" value="<?php echo $jam; ?>" />
<label for="file_laporan_hasil_kpa">Upload File Laporan Hasil KPA</label><br>
<input type="file" id="file_laporan_hasil_kpa" name="file_laporan_hasil_kpa" >

berikut contoh kode sumber php yang bisa anda contoh 
<?php 
  $id_mapping            	= $_POST['id_mapping'];
  $file_laporan_hasil_kpa = htmlspecialchars(addslashes($_FILES['file_laporan_hasil_kpa']['name']));
  $tgl_file_hasil_kpa  = $_POST['tgl_file_hasil_kpa'];
  $jam_file_hasil_kpa  = $_POST['jam_file_hasil_kpa'];


    $sqlfile = "SELECT * FROM mapping_paud_kpa WHERE id_mapping = '$id_mapping' ";
    $resultfile = mysqli_query($conn, $sqlfile);
    $row = mysqli_fetch_array($resultfile);
    
    //hapus gambar
    $folder="../files/upload_file_hasil_kpa/$row[file_laporan_hasil_kpa]";
    unlink($folder);
    
    //input file
    $temp = explode('.', $_FILES['file_laporan_hasil_kpa']['name']);
    $nama_baru = round(microtime(true)) . '.' . end($temp);
    move_uploaded_file($_FILES['file_laporan_hasil_kpa']['tmp_name'], '../../../../simak/files/upload_file_hasil_kpa/'.$nama_baru);

    $sqlupdate = "UPDATE mapping_paud_kpa SET file_laporan_hasil_kpa ='$nama_baru', tgl_file_hasil_kpa ='$tgl_file_hasil_kpa', jam_file_hasil_kpa ='$jam_file_hasil_kpa'
    WHERE id_mapping='$id_mapping' ";
?>

proses upload file "file_laporan_hasil_kpa" terjadi tanpa refresh browser (tanpa reload halaman) dan terjadi secara real-time pada perubahan tombol di kolom "PREVIEW" menjadi "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_kpa.file_laporan_hasil_kpa berhasil terunggah

sampai disini apakah anda mengerti dengan apa yang saya maksudkan?
jika ada pertanyaan yang ingin ditanyakan dipersilahkan

=====================================================================================================================

- "File Pakta Integritas 1" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_visitasi_paud/, untuk nama file diambil dari field mapping_paud_kpa.file_pakta_integritas_1






