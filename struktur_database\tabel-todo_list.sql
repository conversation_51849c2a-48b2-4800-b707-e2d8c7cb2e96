-- <PERSON><PERSON> untuk todo list dashboard
CREATE TABLE IF NOT EXISTS `todo_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(25) NOT NULL,
  `task` text NOT NULL,
  `description` text DEFAULT NULL,
  `deadline` varchar(50) DEFAULT NULL,
  `priority` enum('low','medium','high') DEFAULT 'medium',
  `status` enum('pending','in_progress','completed','cancelled') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` varchar(25) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert sample data
INSERT INTO `todo_list` (`user_id`, `task`, `description`, `deadline`, `priority`, `status`, `created_by`) VALUES
('admin_it', 'Verifikasi data akreditasi SMA Negeri 1 Samarinda', 'Melakukan verifikasi kelengkapan dokumen akreditasi', '2 hari', 'high', 'pending', 'admin_it'),
('admin_it', 'Update data PAUD di Kutai Kartanegara', 'Memperbarui data sekolah PAUD yang baru terdaftar', 'Selesai', 'medium', 'completed', 'admin_it'),
('admin_it', 'Buat laporan bulanan akreditasi', 'Menyusun laporan progress akreditasi bulan ini', '5 hari', 'medium', 'pending', 'admin_it'),
('admin_it', 'Review mapping visitasi bulan ini', 'Meninjau jadwal dan mapping asesor untuk visitasi', '1 minggu', 'low', 'pending', 'admin_it'),
('admin_it', 'Koordinasi dengan asesor untuk jadwal visitasi', 'Mengatur jadwal visitasi dengan tim asesor', '3 hari', 'high', 'pending', 'admin_it');

-- Struktur tabel untuk dokumentasi
/*
Struktur tabel todo_list:
- id: Primary key auto increment
- user_id: ID user yang memiliki todo (foreign key ke tabel user)
- task: Judul tugas
- description: Deskripsi detail tugas (optional)
- deadline: Batas waktu dalam format string (misal: "2 hari", "1 minggu")
- priority: Prioritas tugas (low, medium, high)
- status: Status tugas (pending, in_progress, completed, cancelled)
- created_at: Waktu pembuatan
- updated_at: Waktu update terakhir
- created_by: User yang membuat todo
*/
