<?php
/**
 * Test khusus untuk fitur sebaran sekolah per rumpun
 */

// Include koneksi database
require_once '../../koneksi.php';

// Include session checker
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

$provinsi_id = $_SESSION['provinsi_id'] ?? 64;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Sebaran Sekolah per Rumpun</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9; }
        .result { background-color: #fff; padding: 10px; margin: 10px 0; border-radius: 3px; border-left: 4px solid #007bff; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .progress { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .progress-bar { height: 100%; background: #007bff; transition: width 0.3s; }
    </style>
</head>
<body>
    <h1>🔍 Test Sebaran Sekolah per Rumpun</h1>
    <p><strong>Provinsi ID:</strong> <?php echo $provinsi_id; ?></p>
    
    <div class="box">
        <h2>📊 1. Pivot Table Sebaran Dasmen per Kab/Kota</h2>
        <?php
        $query_dasmen_pivot = "SELECT kk.nm_kota, j.nm_jenjang, COUNT(s.sekolah_id) as total
                               FROM sekolah s
                               LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                               LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
                               WHERE s.provinsi_id = ? AND s.rumpun = 'dasmen'
                               GROUP BY kk.kota_id, kk.nm_kota, j.jenjang_id, j.nm_jenjang
                               ORDER BY kk.nm_kota, j.jenjang_id";

        $stmt = $conn->prepare($query_dasmen_pivot);
        $stmt->bind_param("i", $provinsi_id);
        $stmt->execute();
        $result = $stmt->get_result();

        $dasmen_pivot = [];
        $jenjang_list = [];
        $kota_list = [];

        while ($row = $result->fetch_assoc()) {
            $dasmen_pivot[] = $row;
            if (!in_array($row['nm_jenjang'], $jenjang_list)) {
                $jenjang_list[] = $row['nm_jenjang'];
            }
            if (!in_array($row['nm_kota'], $kota_list)) {
                $kota_list[] = $row['nm_kota'];
            }
        }

        // Buat pivot table
        echo "<div style='overflow-x: auto;'>";
        echo "<table style='font-size: 12px;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px; border: 1px solid #ddd;'>Kab/Kota</th>";

        foreach ($jenjang_list as $jenjang) {
            echo "<th style='padding: 8px; border: 1px solid #ddd; text-align: center;'>$jenjang</th>";
        }
        echo "<th style='padding: 8px; border: 1px solid #ddd; text-align: center; background: #e0e0e0;'><strong>Total</strong></th>";
        echo "</tr>";

        // Group data by kota
        $pivot_data = [];
        foreach ($dasmen_pivot as $item) {
            $pivot_data[$item['nm_kota']][$item['nm_jenjang']] = $item['total'];
        }

        $grand_total = 0;
        foreach ($kota_list as $kota) {
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>$kota</strong></td>";

            $kota_total = 0;
            foreach ($jenjang_list as $jenjang) {
                $count = $pivot_data[$kota][$jenjang] ?? 0;
                $kota_total += $count;
                $bg_color = $count > 0 ? 'background: #f9f9f9;' : '';
                echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center; $bg_color'>" . ($count > 0 ? $count : '-') . "</td>";
            }

            echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center; background: #e0e0e0;'><strong>$kota_total</strong></td>";
            echo "</tr>";
            $grand_total += $kota_total;
        }

        // Total row
        echo "<tr style='background: #d0d0d0;'>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>TOTAL</strong></td>";

        foreach ($jenjang_list as $jenjang) {
            $jenjang_total = 0;
            foreach ($kota_list as $kota) {
                $jenjang_total += $pivot_data[$kota][$jenjang] ?? 0;
            }
            echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'><strong>$jenjang_total</strong></td>";
        }

        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center; background: #c0c0c0;'><strong>$grand_total</strong></td>";
        echo "</tr>";
        echo "</table>";
        echo "</div>";

        echo "<p><strong>Total Dasmen:</strong> $grand_total sekolah</p>";
        echo "<p><strong>Jumlah Kab/Kota:</strong> " . count($kota_list) . "</p>";
        echo "<p><strong>Jumlah Jenjang:</strong> " . count($jenjang_list) . "</p>";
        ?>
    </div>
    
    <div class="box">
        <h2>👶 2. Pivot Table Sebaran PAUD per Kab/Kota</h2>
        <?php
        $query_paud_pivot = "SELECT kk.nm_kota, j.nm_jenjang, COUNT(s.sekolah_id) as total
                             FROM sekolah s
                             LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                             LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
                             WHERE s.provinsi_id = ? AND s.rumpun = 'paud'
                             GROUP BY kk.kota_id, kk.nm_kota, j.jenjang_id, j.nm_jenjang
                             ORDER BY kk.nm_kota, j.jenjang_id";

        $stmt = $conn->prepare($query_paud_pivot);
        $stmt->bind_param("i", $provinsi_id);
        $stmt->execute();
        $result = $stmt->get_result();

        $paud_pivot = [];
        $paud_jenjang_list = [];
        $paud_kota_list = [];

        while ($row = $result->fetch_assoc()) {
            $paud_pivot[] = $row;
            if (!in_array($row['nm_jenjang'], $paud_jenjang_list)) {
                $paud_jenjang_list[] = $row['nm_jenjang'];
            }
            if (!in_array($row['nm_kota'], $paud_kota_list)) {
                $paud_kota_list[] = $row['nm_kota'];
            }
        }

        // Buat pivot table
        echo "<div style='overflow-x: auto;'>";
        echo "<table style='font-size: 12px;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px; border: 1px solid #ddd;'>Kab/Kota</th>";

        foreach ($paud_jenjang_list as $jenjang) {
            echo "<th style='padding: 8px; border: 1px solid #ddd; text-align: center;'>$jenjang</th>";
        }
        echo "<th style='padding: 8px; border: 1px solid #ddd; text-align: center; background: #e0e0e0;'><strong>Total</strong></th>";
        echo "</tr>";

        // Group data by kota
        $paud_pivot_data = [];
        foreach ($paud_pivot as $item) {
            $paud_pivot_data[$item['nm_kota']][$item['nm_jenjang']] = $item['total'];
        }

        $paud_grand_total = 0;
        foreach ($paud_kota_list as $kota) {
            echo "<tr>";
            echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>$kota</strong></td>";

            $kota_total = 0;
            foreach ($paud_jenjang_list as $jenjang) {
                $count = $paud_pivot_data[$kota][$jenjang] ?? 0;
                $kota_total += $count;
                $bg_color = $count > 0 ? 'background: #f9f9f9;' : '';
                echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center; $bg_color'>" . ($count > 0 ? $count : '-') . "</td>";
            }

            echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center; background: #e0e0e0;'><strong>$kota_total</strong></td>";
            echo "</tr>";
            $paud_grand_total += $kota_total;
        }

        // Total row
        echo "<tr style='background: #d0d0d0;'>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'><strong>TOTAL</strong></td>";

        foreach ($paud_jenjang_list as $jenjang) {
            $jenjang_total = 0;
            foreach ($paud_kota_list as $kota) {
                $jenjang_total += $paud_pivot_data[$kota][$jenjang] ?? 0;
            }
            echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center;'><strong>$jenjang_total</strong></td>";
        }

        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: center; background: #c0c0c0;'><strong>$paud_grand_total</strong></td>";
        echo "</tr>";
        echo "</table>";
        echo "</div>";

        echo "<p><strong>Total PAUD:</strong> $paud_grand_total sekolah</p>";
        echo "<p><strong>Jumlah Kab/Kota:</strong> " . count($paud_kota_list) . "</p>";
        echo "<p><strong>Jumlah Jenjang:</strong> " . count($paud_jenjang_list) . "</p>";
        ?>
    </div>
    
    <div class="box">
        <h2>🎯 3. Simulasi Dashboard Data</h2>
        <div class="result">
            <h4>Data yang akan ditampilkan di dashboard:</h4>

            <h5>Tab Dasmen (Pivot Table):</h5>
            <p>Menampilkan <?php echo count($kota_list); ?> kabupaten/kota dengan <?php echo count($jenjang_list); ?> jenjang dalam format pivot table.</p>
            <p><strong>Total Dasmen:</strong> <?php echo $grand_total; ?> sekolah</p>

            <h5>Tab PAUD (Pivot Table):</h5>
            <p>Menampilkan <?php echo count($paud_kota_list); ?> kabupaten/kota dengan <?php echo count($paud_jenjang_list); ?> jenjang dalam format pivot table.</p>
            <p><strong>Total PAUD:</strong> <?php echo $paud_grand_total; ?> sekolah</p>

            <h5>Format Tampilan:</h5>
            <ul>
                <li>✅ Pivot table dengan scroll horizontal</li>
                <li>✅ Header kabupaten/kota di kolom pertama</li>
                <li>✅ Header jenjang di baris pertama</li>
                <li>✅ Cell berisi jumlah sekolah per kombinasi</li>
                <li>✅ Total per baris dan kolom</li>
                <li>✅ Grand total di pojok kanan bawah</li>
            </ul>
        </div>
    </div>
    
    <div class="box">
        <h2>🔧 4. Test API Response</h2>
        <button onclick="testSebaranAPI()" style="padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">Test API</button>
        <div id="api-result" class="result" style="display: none;"></div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script>
    function testSebaranAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '<p class="info">Testing API...</p>';
        
        fetch('ajax/get_dashboard_data.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = '<h4>API Response Success!</h4>';
                    
                    // Test Sebaran Dasmen data
                    html += '<h5>Sebaran Dasmen per Kab/Kota:</h5>';
                    if (data.data.sebaran_dasmen_kota && data.data.sebaran_dasmen_kota.length > 0) {
                        html += `<p>Data pivot: ${data.data.sebaran_dasmen_kota.length} records</p>`;
                        html += '<ul>';
                        data.data.sebaran_dasmen_kota.slice(0, 5).forEach(item => {
                            html += `<li><strong>${item.nm_kota} - ${item.nm_jenjang}:</strong> ${item.total} sekolah</li>`;
                        });
                        if (data.data.sebaran_dasmen_kota.length > 5) {
                            html += `<li><em>... dan ${data.data.sebaran_dasmen_kota.length - 5} data lainnya</em></li>`;
                        }
                        html += '</ul>';
                        html += `<p><strong>Total Dasmen:</strong> ${data.data.total_dasmen}</p>`;
                    } else {
                        html += '<p class="error">No Sebaran Dasmen data</p>';
                    }

                    // Test Sebaran PAUD data
                    html += '<h5>Sebaran PAUD per Kab/Kota:</h5>';
                    if (data.data.sebaran_paud_kota && data.data.sebaran_paud_kota.length > 0) {
                        html += `<p>Data pivot: ${data.data.sebaran_paud_kota.length} records</p>`;
                        html += '<ul>';
                        data.data.sebaran_paud_kota.slice(0, 5).forEach(item => {
                            html += `<li><strong>${item.nm_kota} - ${item.nm_jenjang}:</strong> ${item.total} sekolah</li>`;
                        });
                        if (data.data.sebaran_paud_kota.length > 5) {
                            html += `<li><em>... dan ${data.data.sebaran_paud_kota.length - 5} data lainnya</em></li>`;
                        }
                        html += '</ul>';
                        html += `<p><strong>Total PAUD:</strong> ${data.data.total_paud}</p>`;
                    } else {
                        html += '<p class="error">No Sebaran PAUD data</p>';
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<p class="error">API Error: ${data.message}</p>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<p class="error">Fetch Error: ${error.message}</p>`;
            });
    }
    </script>
    
    <div class="box">
        <h2>📋 Summary</h2>
        <div class="result">
            <?php
            $total_all = $grand_total + $paud_grand_total;
            if ($total_all > 0) {
                echo "<p class='success'>✓ Dashboard siap menampilkan pivot table sebaran per kabupaten/kota</p>";
                echo "<p><strong>Total Sekolah:</strong> $total_all ($grand_total Dasmen + $paud_grand_total PAUD)</p>";
                echo "<p><strong>Pivot Dasmen:</strong> " . count($kota_list) . " kab/kota × " . count($jenjang_list) . " jenjang</p>";
                echo "<p><strong>Pivot PAUD:</strong> " . count($paud_kota_list) . " kab/kota × " . count($paud_jenjang_list) . " jenjang</p>";
                echo "<p><strong>Format:</strong> Tabel pivot dengan scroll horizontal dan total per baris/kolom</p>";
            } else {
                echo "<p class='error'>✗ Tidak ada data untuk ditampilkan di dashboard</p>";
            }
            ?>
        </div>
        
        <p>
            <a href="dashboard.php" target="_blank" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;">🚀 Buka Dashboard</a>
            <a href="test_final.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px; margin-left: 10px;">🔍 Test Final</a>
        </p>
    </div>
    
</body>
</html>
