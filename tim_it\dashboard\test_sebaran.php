<?php
/**
 * Test khusus untuk fitur sebaran sekolah per rumpun
 */

// Include koneksi database
require_once '../../koneksi.php';

// Include session checker
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

$provinsi_id = $_SESSION['provinsi_id'] ?? 64;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Sebaran Sekolah per Rumpun</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9; }
        .result { background-color: #fff; padding: 10px; margin: 10px 0; border-radius: 3px; border-left: 4px solid #007bff; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .progress { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .progress-bar { height: 100%; background: #007bff; transition: width 0.3s; }
    </style>
</head>
<body>
    <h1>🔍 Test Sebaran Sekolah per Rumpun</h1>
    <p><strong>Provinsi ID:</strong> <?php echo $provinsi_id; ?></p>
    
    <div class="box">
        <h2>📊 1. Data Sebaran Dasmen</h2>
        <?php
        $query_dasmen = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
                         FROM sekolah s
                         LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                         WHERE s.provinsi_id = ? AND s.rumpun = 'dasmen'
                         GROUP BY j.jenjang_id, j.nm_jenjang
                         ORDER BY j.jenjang_id";
        
        $stmt = $conn->prepare($query_dasmen);
        $stmt->bind_param("i", $provinsi_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $dasmen_data = [];
        $total_dasmen = 0;
        
        echo "<table>";
        echo "<tr><th>Jenjang</th><th>Total</th><th>Percentage</th><th>Visual</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            $dasmen_data[] = $row;
            $total_dasmen += $row['total'];
        }
        
        foreach ($dasmen_data as $item) {
            $percentage = $total_dasmen > 0 ? ($item['total'] / $total_dasmen * 100) : 0;
            echo "<tr>";
            echo "<td><i class='fas fa-graduation-cap'></i> " . ($item['nm_jenjang'] ?? 'NULL') . "</td>";
            echo "<td>" . $item['total'] . "</td>";
            echo "<td>" . number_format($percentage, 1) . "%</td>";
            echo "<td><div class='progress'><div class='progress-bar' style='width: " . $percentage . "%'></div></div></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "<p><strong>Total Dasmen:</strong> $total_dasmen sekolah</p>";
        
        if ($total_dasmen > 0) {
            echo "<p class='success'>✓ Data Dasmen tersedia untuk dashboard</p>";
        } else {
            echo "<p class='error'>✗ Tidak ada data Dasmen</p>";
        }
        ?>
    </div>
    
    <div class="box">
        <h2>👶 2. Data Sebaran PAUD</h2>
        <?php
        $query_paud = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
                       FROM sekolah s
                       LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                       WHERE s.provinsi_id = ? AND s.rumpun = 'paud'
                       GROUP BY j.jenjang_id, j.nm_jenjang
                       ORDER BY j.jenjang_id";
        
        $stmt = $conn->prepare($query_paud);
        $stmt->bind_param("i", $provinsi_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $paud_data = [];
        $total_paud = 0;
        
        echo "<table>";
        echo "<tr><th>Jenjang</th><th>Total</th><th>Percentage</th><th>Visual</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            $paud_data[] = $row;
            $total_paud += $row['total'];
        }
        
        foreach ($paud_data as $item) {
            $percentage = $total_paud > 0 ? ($item['total'] / $total_paud * 100) : 0;
            echo "<tr>";
            echo "<td><i class='fas fa-child'></i> " . ($item['nm_jenjang'] ?? 'NULL') . "</td>";
            echo "<td>" . $item['total'] . "</td>";
            echo "<td>" . number_format($percentage, 1) . "%</td>";
            echo "<td><div class='progress'><div class='progress-bar' style='width: " . $percentage . "%'></div></div></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "<p><strong>Total PAUD:</strong> $total_paud sekolah</p>";
        
        if ($total_paud > 0) {
            echo "<p class='success'>✓ Data PAUD tersedia untuk dashboard</p>";
        } else {
            echo "<p class='error'>✗ Tidak ada data PAUD</p>";
        }
        ?>
    </div>
    
    <div class="box">
        <h2>🎯 3. Simulasi Dashboard Data</h2>
        <div class="result">
            <h4>Data yang akan ditampilkan di dashboard:</h4>
            
            <h5>Tab Dasmen:</h5>
            <ul>
                <?php foreach ($dasmen_data as $item): ?>
                    <?php $percentage = $total_dasmen > 0 ? ($item['total'] / $total_dasmen * 100) : 0; ?>
                    <li>
                        <strong><?php echo $item['nm_jenjang']; ?>:</strong> 
                        <?php echo $item['total']; ?> sekolah 
                        (<?php echo number_format($percentage, 1); ?>%)
                    </li>
                <?php endforeach; ?>
            </ul>
            
            <h5>Tab PAUD:</h5>
            <ul>
                <?php foreach ($paud_data as $item): ?>
                    <?php $percentage = $total_paud > 0 ? ($item['total'] / $total_paud * 100) : 0; ?>
                    <li>
                        <strong><?php echo $item['nm_jenjang']; ?>:</strong> 
                        <?php echo $item['total']; ?> sekolah 
                        (<?php echo number_format($percentage, 1); ?>%)
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    
    <div class="box">
        <h2>🔧 4. Test API Response</h2>
        <button onclick="testSebaranAPI()" style="padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">Test API</button>
        <div id="api-result" class="result" style="display: none;"></div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script>
    function testSebaranAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '<p class="info">Testing API...</p>';
        
        fetch('ajax/get_dashboard_data.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = '<h4>API Response Success!</h4>';
                    
                    // Test Dasmen data
                    html += '<h5>Dasmen Data:</h5>';
                    if (data.data.sekolah_dasmen && data.data.sekolah_dasmen.length > 0) {
                        html += '<ul>';
                        data.data.sekolah_dasmen.forEach(item => {
                            const percentage = data.data.total_dasmen > 0 ? (item.total / data.data.total_dasmen * 100).toFixed(1) : 0;
                            html += `<li><strong>${item.nm_jenjang}:</strong> ${item.total} sekolah (${percentage}%)</li>`;
                        });
                        html += '</ul>';
                        html += `<p><strong>Total Dasmen:</strong> ${data.data.total_dasmen}</p>`;
                    } else {
                        html += '<p class="error">No Dasmen data</p>';
                    }
                    
                    // Test PAUD data
                    html += '<h5>PAUD Data:</h5>';
                    if (data.data.sekolah_paud && data.data.sekolah_paud.length > 0) {
                        html += '<ul>';
                        data.data.sekolah_paud.forEach(item => {
                            const percentage = data.data.total_paud > 0 ? (item.total / data.data.total_paud * 100).toFixed(1) : 0;
                            html += `<li><strong>${item.nm_jenjang}:</strong> ${item.total} sekolah (${percentage}%)</li>`;
                        });
                        html += '</ul>';
                        html += `<p><strong>Total PAUD:</strong> ${data.data.total_paud}</p>`;
                    } else {
                        html += '<p class="error">No PAUD data</p>';
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<p class="error">API Error: ${data.message}</p>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<p class="error">Fetch Error: ${error.message}</p>`;
            });
    }
    </script>
    
    <div class="box">
        <h2>📋 Summary</h2>
        <div class="result">
            <?php
            $total_all = $total_dasmen + $total_paud;
            if ($total_all > 0) {
                echo "<p class='success'>✓ Dashboard siap menampilkan data sebaran per rumpun</p>";
                echo "<p><strong>Total Sekolah:</strong> $total_all ($total_dasmen Dasmen + $total_paud PAUD)</p>";
                echo "<p><strong>Fitur Tab:</strong> Dasmen (" . count($dasmen_data) . " jenjang) | PAUD (" . count($paud_data) . " jenjang)</p>";
            } else {
                echo "<p class='error'>✗ Tidak ada data untuk ditampilkan di dashboard</p>";
            }
            ?>
        </div>
        
        <p>
            <a href="dashboard.php" target="_blank" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;">🚀 Buka Dashboard</a>
            <a href="test_final.php" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px; margin-left: 10px;">🔍 Test Final</a>
        </p>
    </div>
    
</body>
</html>
