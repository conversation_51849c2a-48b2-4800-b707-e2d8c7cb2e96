<?php
/**
 * AJAX handler untuk delete mapping visitasi
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Ambil dan validasi input
    $id_mapping = intval($_POST['id_mapping'] ?? 0);
    $provinsi_id = intval($_SESSION['provinsi_id']);

    // Validasi required fields
    if ($id_mapping <= 0) {
        echo json_encode(['success' => false, 'message' => 'ID mapping tidak valid']);
        exit;
    }

    // Cek apakah mapping exists dan milik provinsi yang benar
    $check_query = "SELECT m.id_mapping, s.nama_sekolah, s.npsn 
                   FROM mapping_2024 m
                   LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
                   WHERE m.id_mapping = $id_mapping AND m.provinsi_id = $provinsi_id";
    $check_result = $conn->query($check_query);

    if (!$check_result) {
        echo json_encode(['success' => false, 'message' => 'Query error: ' . $conn->error]);
        exit;
    }

    if ($check_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Data mapping tidak ditemukan atau tidak memiliki akses']);
        exit;
    }

    $mapping_data = $check_result->fetch_assoc();

    // Hapus data mapping
    $delete_query = "DELETE FROM mapping_2024 
                    WHERE id_mapping = $id_mapping AND provinsi_id = $provinsi_id";

    if ($conn->query($delete_query)) {
        // Cek apakah data benar-benar terhapus
        if ($conn->affected_rows > 0) {
            // Log activity
            error_log("Mapping Deleted - ID: $id_mapping, Sekolah: " . ($mapping_data['nama_sekolah'] ?? 'Unknown') . 
                     ", NPSN: " . ($mapping_data['npsn'] ?? 'Unknown') . 
                     ", User: " . ($_SESSION['nm_user'] ?? 'Unknown'));

            echo json_encode([
                'success' => true,
                'message' => 'Data mapping berhasil dihapus',
                'data' => [
                    'id_mapping' => $id_mapping,
                    'nama_sekolah' => $mapping_data['nama_sekolah'] ?? 'Unknown',
                    'npsn' => $mapping_data['npsn'] ?? 'Unknown'
                ]
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Data tidak ditemukan atau sudah terhapus sebelumnya'
            ]);
        }
    } else {
        error_log("Delete Mapping Failed: " . $conn->error);
        echo json_encode([
            'success' => false,
            'message' => 'Gagal menghapus data mapping: ' . $conn->error
        ]);
    }

} catch (Exception $e) {
    error_log("Delete Mapping Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
