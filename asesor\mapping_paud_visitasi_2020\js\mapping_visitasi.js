/**
 * JavaScript untuk Modul Mapping Visitasi PAUD
 * Version: 1.0.0
 */

// Wait for jQuery to be available
function waitForJQuery() {
    if (typeof jQuery !== 'undefined') {
        $(document).ready(function() {
            console.log('DOM ready, initializing mapping visitasi page');
            // Initialize page
            initializePage();
        });
    } else {
        console.log('Waiting for jQuery...');
        setTimeout(waitForJQuery, 100);
    }
}

// Start waiting for jQuery
waitForJQuery();

/**
 * Initialize page functionality
 */
function initializePage() {
    // Initialize page functionality
    setupEventListeners();
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Handle file input change dengan event delegation
    $(document).on('change', '#file_visitasi', function() {
        updateFileLabel(this);
    });

    // Backup handler dengan input event
    $(document).on('input', '#file_visitasi', function() {
        updateFileLabel(this);
    });

    // Handle upload form submit (backup prevention)
    $(document).on('submit', '#uploadForm', function(e) {
        e.preventDefault();
        e.stopPropagation();
        handleFileUpload();
        return false;
    });

    // Handle modal close - reset semua state
    $('#uploadModal').on('hidden.bs.modal', function() {
        resetModalState();
    });

    // Handle modal show - setup file input handlers
    $('#uploadModal').on('shown.bs.modal', function() {
        const $fileInput = $('#file_visitasi');
        const $fileLabel = $fileInput.next('.custom-file-label');

        // Backup event handlers untuk modal
        $fileInput.off('change.modal').on('change.modal', function() {
            updateFileLabel(this);
        });

        $fileInput.off('input.modal').on('input.modal', function() {
            updateFileLabel(this);
        });
    });
}

/**
 * Update file label dengan nama file yang dipilih
 * @param {HTMLElement} fileInput - File input element
 */
function updateFileLabel(fileInput) {
    const fileName = fileInput.value.split('\\').pop();
    const $label = $(fileInput).next('.custom-file-label');

    if (fileName && fileName.trim() !== '') {
        $label.text(fileName);
        $label.addClass('file-selected');
    } else {
        $label.text(''); // Kosongkan label jika tidak ada file
        $label.removeClass('file-selected');
    }
}

/**
 * Reset modal state ke kondisi awal
 */
function resetModalState() {
    // Reset form
    $('#uploadForm')[0].reset();

    // Reset file label (kosongkan)
    $('#file_visitasi').next('.custom-file-label').html('');

    // Reset progress bar
    $('#uploadProgress').hide();
    $('#progressBar').css('width', '0%')
                     .removeClass('bg-success bg-danger')
                     .addClass('progress-bar-striped progress-bar-animated');
    $('#progressText').text('0%');

    // Reset upload button
    $('#uploadBtn').prop('disabled', false)
                   .removeClass('loading')
                   .html('<i class="fas fa-upload mr-1"></i>Upload File');

    // Modal state reset complete
}

/**
 * Upload file function - Show modal
 * @param {number} idMapping - ID mapping yang akan diupload
 * @param {string} fileType - Jenis file yang akan diupload
 */
function uploadFile(idMapping, fileType) {
    // Reset modal state terlebih dahulu
    resetModalState();

    // Set data ke form
    $('#id_mapping').val(idMapping);
    $('#file_type').val(fileType);
    
    // Set modal title dan label berdasarkan file type
    const fileLabels = {
        'pakta_integritas_1': 'Upload File Pakta Integritas Asesor A',
        'pakta_integritas_2': 'Upload File Pakta Integritas Asesor B',
        'berita_acara_visitasi': 'Upload File Berita Acara Visitasi',
        'temuan_hasil_visitasi': 'Upload File Temuan Hasil Visitasi',
        'absen_pembuka': 'Upload File Absen Pembuka',
        'absen_penutup': 'Upload File Absen Penutup',
        'foto_visitasi': 'Upload File Foto Visitasi',
        'laporan_individu_1': 'Upload File Laporan Individu A',
        'laporan_individu_2': 'Upload File Laporan Individu B',
        'laporan_kelompok': 'Upload File Laporan Kelompok',
        'penjelasan_hasil_akreditasi': 'Upload File Penjelasan Hasil Akreditasi (PHA)'
    };
    
    const modalTitle = fileLabels[fileType] || 'Upload File';
    $('#uploadModalLabel').text(modalTitle);
    $('#fileLabel').html('<i class="fas fa-file-pdf mr-1"></i>' + modalTitle);

    // Show modal
    $('#uploadModal').modal('show');
}

/**
 * Handle file upload with progress bar
 */
function handleFileUpload() {
    const formData = new FormData($('#uploadForm')[0]);
    const uploadBtn = $('#uploadBtn');
    const progressContainer = $('#uploadProgress');
    const progressBar = $('#progressBar');
    const progressText = $('#progressText');
    
    // Validate file
    const fileInput = $('#file_visitasi')[0];

    if (!fileInput.files.length) {
        showError('Silahkan pilih file terlebih dahulu');
        return;
    }
    
    // Show progress bar
    progressContainer.show();
    progressBar.css('width', '0%');
    progressText.text('0%');
    
    // Disable upload button dengan professional loading state
    uploadBtn.prop('disabled', true)
             .addClass('loading')
             .html('<i class="fas fa-spinner fa-spin mr-1"></i>Uploading...');
    
    // AJAX upload with progress
    $.ajax({
        url: 'ajax/upload_file.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            
            // Upload progress
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    progressBar.css('width', percentComplete + '%');
                    progressText.text(percentComplete + '%');
                }
            }, false);
            
            return xhr;
        },
        success: function(response) {
            if (response.success) {
                // Success
                progressBar.removeClass('progress-bar-striped progress-bar-animated')
                          .addClass('bg-success');
                progressText.text('Complete!');
                
                // Show success message
                showSuccess(response.message);

                // Update UI - real-time update tanpa reload
                updateFileStatus(response.data.id_mapping, response.data.file_type, response.data.filename);

                // Reset button state dan close modal
                uploadBtn.prop('disabled', false)
                         .removeClass('loading')
                         .html('<i class="fas fa-upload mr-1"></i>Upload File');

                // Auto close modal after success
                setTimeout(function() {
                    $('#uploadModal').modal('hide');
                }, 1000);
                
            } else {
                // Error
                progressBar.removeClass('progress-bar-striped progress-bar-animated')
                          .addClass('bg-danger');
                progressText.text('Failed!');

                // Show detailed error for debugging
                console.error('Upload error:', response);
                showError(response.message || 'File gagal di-upload, silahkan upload ulang');

                // Re-enable upload button
                uploadBtn.prop('disabled', false)
                         .removeClass('loading')
                         .html('<i class="fas fa-upload mr-1"></i>Upload File');
            }
        },
        error: function(xhr, status, error) {
            // AJAX Error
            progressBar.removeClass('progress-bar-striped progress-bar-animated')
                      .addClass('bg-danger');
            progressText.text('Error!');

            // Log detailed error for debugging
            console.error('AJAX Error:', {
                status: status,
                error: error,
                responseText: xhr.responseText
            });

            showError('File gagal di-upload, silahkan upload ulang');

            // Re-enable upload button
            uploadBtn.prop('disabled', false)
                     .removeClass('loading')
                     .html('<i class="fas fa-upload mr-1"></i>Upload File');
        }
    });
}

/**
 * Update file status in table real-time
 * @param {number} idMapping - ID mapping
 * @param {string} fileType - File type yang diupload
 * @param {string} filename - Nama file yang diupload
 */
function updateFileStatus(idMapping, fileType, filename) {
    // File type mapping untuk mencari label yang tepat
    const fileLabels = {
        'pakta_integritas_1': 'File Pakta Integritas Asesor A',
        'pakta_integritas_2': 'File Pakta Integritas Asesor B',
        'berita_acara_visitasi': 'File Berita Acara Visitasi',
        'temuan_hasil_visitasi': 'File Temuan Hasil Visitasi',
        'absen_pembuka': 'File Absen Pembuka',
        'absen_penutup': 'File Absen Penutup',
        'foto_visitasi': 'File Foto Visitasi',
        'laporan_individu_1': 'File Laporan Individu A',
        'laporan_individu_2': 'File Laporan Individu B',
        'laporan_kelompok': 'File Laporan Kelompok',
        'penjelasan_hasil_akreditasi': 'File Penjelasan Hasil Akreditasi (PHA)'
    };

    const labelText = fileLabels[fileType];

    // Cari row yang sesuai dengan ID mapping
    $('button[onclick*="' + idMapping + '"]').closest('tr').find('td').eq(4).find('div').each(function() {
        const $div = $(this);
        const $label = $div.find('strong');

        if ($label.text().includes(labelText.replace('File ', ''))) {
            const $badge = $div.find('.badge');

            // Smooth transition dengan fade effect
            $badge.fadeOut(200, function() {
                // Update badge menjadi "Sudah Upload" dengan preview
                $(this).removeClass('badge-danger')
                       .addClass('badge-success')
                       .html('<i class="fas fa-eye mr-1"></i>Sudah Upload')
                       .attr('onclick', 'previewFile("' + filename + '")')
                       .css('cursor', 'pointer')
                       .fadeIn(300);
            });
        }
    });
}

/**
 * Preview uploaded file in new tab
 * @param {string} filename - Filename to preview
 */
function previewFile(filename) {
    if (!filename) {
        showError('File tidak ditemukan');
        return;
    }

    // Use secure file handler
    const fileUrl = 'preview_file.php?file=' + encodeURIComponent(filename);

    // Open in new tab
    window.open(fileUrl, '_blank');
}

/**
 * Input tanggal visitasi
 * @param {number} idMapping - ID mapping
 */
function inputTanggal(idMapping) {
    $('#tanggal_id_mapping').val(idMapping);
    $('#tgl_visitasi').val('');
    $('#tanggalModal').modal('show');
}

/**
 * Update tanggal visitasi di tabel secara real-time
 * @param {number} idMapping - ID mapping
 * @param {string} tglVisitasi - Tanggal visitasi (YYYY-MM-DD)
 */
function updateTanggalVisitasi(idMapping, tglVisitasi) {
    // Convert ke format dd-mm-yyyy untuk display
    const dateParts = tglVisitasi.split('-');
    const displayDate = dateParts[2] + '-' + dateParts[1] + '-' + dateParts[0];

    // Cari row yang sesuai dengan ID mapping dan update tanggal dengan animation
    const $tanggalElement = $('button[onclick*="' + idMapping + '"]').closest('tr').find('td').eq(5).find('div').first().find('.text-info');

    // Smooth update dengan highlight effect
    $tanggalElement.fadeOut(200, function() {
        $(this).text(displayDate)
               .addClass('updated')
               .fadeIn(300);

        // Remove highlight class after animation
        setTimeout(function() {
            $tanggalElement.removeClass('updated');
        }, 1000);
    });
}

/**
 * Save tanggal visitasi
 */
function saveTanggal() {
    const idMapping = $('#tanggal_id_mapping').val();
    const tglVisitasi = $('#tgl_visitasi').val();
    
    if (!tglVisitasi) {
        showError('Silahkan pilih tanggal visitasi');
        return;
    }
    
    $.ajax({
        url: 'ajax/save_tanggal.php',
        type: 'POST',
        data: {
            id_mapping: idMapping,
            tgl_visitasi: tglVisitasi
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showSuccess(response.message);
                $('#tanggalModal').modal('hide');

                // Update tanggal di tabel secara real-time
                updateTanggalVisitasi(response.data.id_mapping, response.data.tgl_visitasi);
            } else {
                showError(response.message);
            }
        },
        error: function() {
            showError('Gagal menyimpan tanggal visitasi');
        }
    });
}

/**
 * Download surat tugas
 * @param {number} idMapping - ID mapping
 */
function downloadSuratTugas(idMapping) {
    // Placeholder - implement download logic
    showInfo('Fitur download surat tugas akan diimplementasi');
}

/**
 * Upload surat tugas
 * @param {number} idMapping - ID mapping
 */
function uploadSuratTugas(idMapping) {
    // Placeholder - implement upload surat tugas
    showInfo('Fitur upload surat tugas akan diimplementasi');
}

/**
 * Show success message dengan professional toast
 * @param {string} message - Success message
 */
function showSuccess(message) {
    Swal.fire({
        title: 'Berhasil!',
        text: message,
        icon: 'success',
        confirmButtonColor: '#28a745',
        timer: 2000,
        timerProgressBar: true,
        showConfirmButton: false,
        toast: true,
        position: 'top-end',
        background: '#d4edda',
        color: '#155724'
    });
}

/**
 * Show error message
 * @param {string} message - Error message
 */
function showError(message) {
    Swal.fire({
        title: 'Error!',
        text: message,
        icon: 'error',
        confirmButtonColor: '#dc3545'
    });
}

/**
 * Show info message
 * @param {string} message - Info message
 */
function showInfo(message) {
    Swal.fire({
        title: 'Info',
        text: message,
        icon: 'info',
        confirmButtonColor: '#007bff'
    });
}
