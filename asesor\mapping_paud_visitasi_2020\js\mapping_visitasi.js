/**
 * JavaScript untuk Modul Mapping Visitasi PAUD
 * Version: 1.0.0
 */

$(document).ready(function() {
    // Initialize page
    initializePage();
});

/**
 * Initialize page functionality
 */
function initializePage() {
    // Initialize page functionality
    setupEventListeners();
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Handle file input change
    $('#file_visitasi').on('change', function() {
        const fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName || 'Pilih file PDF...');
    });
    
    // Handle upload form submit (backup prevention)
    $(document).on('submit', '#uploadForm', function(e) {
        e.preventDefault();
        e.stopPropagation();
        handleFileUpload();
        return false;
    });
}

/**
 * Upload file function - Show modal
 * @param {number} idMapping - ID mapping yang akan diupload
 * @param {string} fileType - Jenis file yang akan diupload
 */
function uploadFile(idMapping, fileType) {
    // Set data ke form
    $('#id_mapping').val(idMapping);
    $('#file_type').val(fileType);
    
    // Reset form
    $('#uploadForm')[0].reset();
    
    // Set data again after reset (important!)
    $('#id_mapping').val(idMapping);
    $('#file_type').val(fileType);
    
    // Set modal title dan label berdasarkan file type
    const fileLabels = {
        'pakta_integritas_1': 'Upload File Pakta Integritas Asesor A',
        'pakta_integritas_2': 'Upload File Pakta Integritas Asesor B',
        'berita_acara_visitasi': 'Upload File Berita Acara Visitasi',
        'temuan_hasil_visitasi': 'Upload File Temuan Hasil Visitasi',
        'absen_pembuka': 'Upload File Absen Pembuka',
        'absen_penutup': 'Upload File Absen Penutup',
        'foto_visitasi': 'Upload File Foto Visitasi',
        'laporan_individu_1': 'Upload File Laporan Individu A',
        'laporan_individu_2': 'Upload File Laporan Individu B',
        'laporan_kelompok': 'Upload File Laporan Kelompok',
        'penjelasan_hasil_akreditasi': 'Upload File Penjelasan Hasil Akreditasi (PHA)'
    };
    
    const modalTitle = fileLabels[fileType] || 'Upload File';
    $('#uploadModalLabel').text(modalTitle);
    $('#fileLabel').html('<i class="fas fa-file-pdf mr-1"></i>' + modalTitle);
    
    // Reset file label
    $('#file_visitasi').next('.custom-file-label').html('Pilih file PDF...');
    
    // Hide progress bar
    $('#uploadProgress').hide();
    $('#progressBar').css('width', '0%');
    $('#progressText').text('0%');
    
    // Enable upload button
    $('#uploadBtn').prop('disabled', false).html('<i class="fas fa-upload mr-1"></i>Upload File');
    
    // Show modal
    $('#uploadModal').modal('show');
}

/**
 * Handle file upload with progress bar
 */
function handleFileUpload() {
    const formData = new FormData($('#uploadForm')[0]);
    const uploadBtn = $('#uploadBtn');
    const progressContainer = $('#uploadProgress');
    const progressBar = $('#progressBar');
    const progressText = $('#progressText');
    
    // Validate file
    const fileInput = $('#file_visitasi')[0];
    if (!fileInput.files.length) {
        showError('Silahkan pilih file terlebih dahulu');
        return;
    }
    
    // Show progress bar
    progressContainer.show();
    progressBar.css('width', '0%');
    progressText.text('0%');
    
    // Disable upload button
    uploadBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>Uploading...');
    
    // AJAX upload with progress
    $.ajax({
        url: 'ajax/upload_file.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            
            // Upload progress
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    progressBar.css('width', percentComplete + '%');
                    progressText.text(percentComplete + '%');
                }
            }, false);
            
            return xhr;
        },
        success: function(response) {
            if (response.success) {
                // Success
                progressBar.removeClass('progress-bar-striped progress-bar-animated')
                          .addClass('bg-success');
                progressText.text('Complete!');
                
                // Show success message
                showSuccess(response.message);
                
                // Update UI - refresh page to show updated status
                setTimeout(function() {
                    $('#uploadModal').modal('hide');
                    location.reload();
                }, 1500);
                
            } else {
                // Error
                progressBar.removeClass('progress-bar-striped progress-bar-animated')
                          .addClass('bg-danger');
                progressText.text('Failed!');

                // Show detailed error for debugging
                console.error('Upload error:', response);
                showError(response.message || 'File gagal di-upload, silahkan upload ulang');

                // Re-enable upload button
                uploadBtn.prop('disabled', false).html('<i class="fas fa-upload mr-1"></i>Upload File');
            }
        },
        error: function(xhr, status, error) {
            // AJAX Error
            progressBar.removeClass('progress-bar-striped progress-bar-animated')
                      .addClass('bg-danger');
            progressText.text('Error!');

            // Log detailed error for debugging
            console.error('AJAX Error:', {
                status: status,
                error: error,
                responseText: xhr.responseText
            });

            showError('File gagal di-upload, silahkan upload ulang');

            // Re-enable upload button
            uploadBtn.prop('disabled', false).html('<i class="fas fa-upload mr-1"></i>Upload File');
        }
    });
}

/**
 * Preview uploaded file in new tab
 * @param {string} filename - Filename to preview
 */
function previewFile(filename) {
    if (!filename) {
        showError('File tidak ditemukan');
        return;
    }
    
    // Use secure file handler
    const fileUrl = 'preview_file.php?file=' + encodeURIComponent(filename);
    
    // Open in new tab
    window.open(fileUrl, '_blank');
}

/**
 * Input tanggal visitasi
 * @param {number} idMapping - ID mapping
 */
function inputTanggal(idMapping) {
    $('#tanggal_id_mapping').val(idMapping);
    $('#tgl_visitasi').val('');
    $('#tanggalModal').modal('show');
}

/**
 * Save tanggal visitasi
 */
function saveTanggal() {
    const idMapping = $('#tanggal_id_mapping').val();
    const tglVisitasi = $('#tgl_visitasi').val();
    
    if (!tglVisitasi) {
        showError('Silahkan pilih tanggal visitasi');
        return;
    }
    
    $.ajax({
        url: 'ajax/save_tanggal.php',
        type: 'POST',
        data: {
            id_mapping: idMapping,
            tgl_visitasi: tglVisitasi
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showSuccess(response.message);
                $('#tanggalModal').modal('hide');
                setTimeout(function() {
                    location.reload();
                }, 1000);
            } else {
                showError(response.message);
            }
        },
        error: function() {
            showError('Gagal menyimpan tanggal visitasi');
        }
    });
}

/**
 * Download surat tugas
 * @param {number} idMapping - ID mapping
 */
function downloadSuratTugas(idMapping) {
    // Placeholder - implement download logic
    showInfo('Fitur download surat tugas akan diimplementasi');
}

/**
 * Upload surat tugas
 * @param {number} idMapping - ID mapping
 */
function uploadSuratTugas(idMapping) {
    // Placeholder - implement upload surat tugas
    showInfo('Fitur upload surat tugas akan diimplementasi');
}

/**
 * Show success message
 * @param {string} message - Success message
 */
function showSuccess(message) {
    Swal.fire({
        title: 'Berhasil!',
        text: message,
        icon: 'success',
        confirmButtonColor: '#28a745'
    });
}

/**
 * Show error message
 * @param {string} message - Error message
 */
function showError(message) {
    Swal.fire({
        title: 'Error!',
        text: message,
        icon: 'error',
        confirmButtonColor: '#dc3545'
    });
}

/**
 * Show info message
 * @param {string} message - Info message
 */
function showInfo(message) {
    Swal.fire({
        title: 'Info',
        text: message,
        icon: 'info',
        confirmButtonColor: '#007bff'
    });
}
