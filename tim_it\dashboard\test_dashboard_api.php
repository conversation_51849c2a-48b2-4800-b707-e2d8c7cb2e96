<?php
/**
 * Test khusus untuk API dashboard dan frontend
 */

// Include koneksi database
require_once '../../koneksi.php';

// Include session checker
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Dashboard API & Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .result { background-color: #f9f9f9; padding: 10px; margin: 10px 0; border-radius: 3px; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test Dashboard API & Frontend</h1>
    
    <div class="box">
        <h2>1. Test API Endpoint</h2>
        <button onclick="testAPI()">Test get_dashboard_data.php</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="box">
        <h2>2. Test Frontend JavaScript</h2>
        <button onclick="testFrontend()">Test Dashboard Functions</button>
        <div id="frontend-result" class="result"></div>
    </div>
    
    <div class="box">
        <h2>3. Test Chart Elements</h2>
        <button onclick="testChartElements()">Test Chart DOM Elements</button>
        <div id="chart-result" class="result"></div>
    </div>
    
    <div class="box">
        <h2>4. Simulate Dashboard Load</h2>
        <button onclick="simulateDashboard()">Simulate Full Dashboard</button>
        <div id="simulate-result" class="result"></div>
        
        <!-- Mini dashboard elements for testing -->
        <div style="display: none;">
            <span id="total-dasmen">-</span>
            <span id="total-paud">-</span>
            <span id="total-kesetaraan">-</span>
            <canvas id="jenjangChart" width="100" height="100"></canvas>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
    let dashboardData = null;
    
    // Test 1: API Endpoint
    function testAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.innerHTML = '<p class="info">Testing API...</p>';
        
        fetch('ajax/get_dashboard_data.php')
            .then(response => {
                console.log('API Response Status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('API Response Data:', data);
                
                if (data.success) {
                    let html = '<p class="success">✓ API Success!</p>';
                    html += '<p><strong>Total Dasmen:</strong> ' + data.data.total_dasmen + '</p>';
                    html += '<p><strong>Total PAUD:</strong> ' + data.data.total_paud + '</p>';
                    html += '<p><strong>Total Kesetaraan:</strong> ' + data.data.total_kesetaraan + '</p>';
                    html += '<p><strong>Akreditasi Rumpun:</strong> ' + data.data.akreditasi_rumpun.length + ' items</p>';
                    html += '<p><strong>Akreditasi Jenjang:</strong> ' + data.data.akreditasi_jenjang.length + ' items</p>';
                    html += '<p><strong>Akreditasi Kota:</strong> ' + data.data.akreditasi_kota.length + ' items</p>';
                    
                    // Store data for other tests
                    dashboardData = data.data;
                    
                    html += '<details><summary>Full Response</summary><pre>' + JSON.stringify(data, null, 2) + '</pre></details>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<p class="error">✗ API Error: ' + data.message + '</p>';
                }
            })
            .catch(error => {
                console.error('API Fetch Error:', error);
                resultDiv.innerHTML = '<p class="error">✗ Fetch Error: ' + error.message + '</p>';
            });
    }
    
    // Test 2: Frontend Functions
    function testFrontend() {
        const resultDiv = document.getElementById('frontend-result');
        
        if (!dashboardData) {
            resultDiv.innerHTML = '<p class="error">✗ No dashboard data. Run API test first.</p>';
            return;
        }
        
        let html = '<p class="info">Testing frontend functions...</p>';
        
        try {
            // Test data validation
            if (dashboardData.sekolah_dasmen && Array.isArray(dashboardData.sekolah_dasmen)) {
                html += '<p class="success">✓ Dasmen data is valid array</p>';
            } else {
                html += '<p class="error">✗ Dasmen data is not valid array</p>';
            }
            
            if (dashboardData.sekolah_paud && Array.isArray(dashboardData.sekolah_paud)) {
                html += '<p class="success">✓ PAUD data is valid array</p>';
            } else {
                html += '<p class="error">✗ PAUD data is not valid array</p>';
            }
            
            if (dashboardData.akreditasi_rumpun && Array.isArray(dashboardData.akreditasi_rumpun)) {
                html += '<p class="success">✓ Akreditasi rumpun data is valid array</p>';
            } else {
                html += '<p class="error">✗ Akreditasi rumpun data is not valid array</p>';
            }
            
            // Test totals
            const totalDasmen = dashboardData.total_dasmen || 0;
            const totalPaud = dashboardData.total_paud || 0;
            const totalKesetaraan = dashboardData.total_kesetaraan || 0;
            
            html += '<p><strong>Calculated Totals:</strong></p>';
            html += '<p>Dasmen: ' + totalDasmen + '</p>';
            html += '<p>PAUD: ' + totalPaud + '</p>';
            html += '<p>Kesetaraan: ' + totalKesetaraan + '</p>';
            html += '<p>Grand Total: ' + (totalDasmen + totalPaud + totalKesetaraan) + '</p>';
            
        } catch (error) {
            html += '<p class="error">✗ Frontend test error: ' + error.message + '</p>';
        }
        
        resultDiv.innerHTML = html;
    }
    
    // Test 3: Chart Elements
    function testChartElements() {
        const resultDiv = document.getElementById('chart-result');
        let html = '<p class="info">Testing chart elements...</p>';
        
        // Check if Chart.js is loaded
        if (typeof Chart !== 'undefined') {
            html += '<p class="success">✓ Chart.js is loaded</p>';
        } else {
            html += '<p class="error">✗ Chart.js is not loaded</p>';
        }
        
        // Check if jQuery is loaded
        if (typeof $ !== 'undefined') {
            html += '<p class="success">✓ jQuery is loaded</p>';
        } else {
            html += '<p class="error">✗ jQuery is not loaded</p>';
        }
        
        // Check chart canvas element
        const chartElement = document.getElementById('jenjangChart');
        if (chartElement) {
            html += '<p class="success">✓ Chart canvas element exists</p>';
            
            // Test chart creation
            try {
                const ctx = chartElement.getContext('2d');
                html += '<p class="success">✓ Canvas context is available</p>';
            } catch (error) {
                html += '<p class="error">✗ Canvas context error: ' + error.message + '</p>';
            }
        } else {
            html += '<p class="error">✗ Chart canvas element not found</p>';
        }
        
        resultDiv.innerHTML = html;
    }
    
    // Test 4: Simulate Dashboard
    function simulateDashboard() {
        const resultDiv = document.getElementById('simulate-result');
        
        if (!dashboardData) {
            resultDiv.innerHTML = '<p class="error">✗ No dashboard data. Run API test first.</p>';
            return;
        }
        
        let html = '<p class="info">Simulating dashboard update...</p>';
        
        try {
            // Update totals
            $('#total-dasmen').text((dashboardData.total_dasmen || 0).toLocaleString());
            $('#total-paud').text((dashboardData.total_paud || 0).toLocaleString());
            $('#total-kesetaraan').text((dashboardData.total_kesetaraan || 0).toLocaleString());
            
            html += '<p class="success">✓ Totals updated successfully</p>';
            html += '<p>Dasmen: ' + $('#total-dasmen').text() + '</p>';
            html += '<p>PAUD: ' + $('#total-paud').text() + '</p>';
            html += '<p>Kesetaraan: ' + $('#total-kesetaraan').text() + '</p>';
            
            // Test chart data processing
            const jenjangData = {};
            
            if (dashboardData.sekolah_dasmen && Array.isArray(dashboardData.sekolah_dasmen)) {
                dashboardData.sekolah_dasmen.forEach(item => {
                    if (item && item.nm_jenjang && item.total) {
                        jenjangData[item.nm_jenjang] = (jenjangData[item.nm_jenjang] || 0) + parseInt(item.total);
                    }
                });
            }
            
            html += '<p class="success">✓ Chart data processed</p>';
            html += '<p>Jenjang data: ' + JSON.stringify(jenjangData) + '</p>';
            
        } catch (error) {
            html += '<p class="error">✗ Simulation error: ' + error.message + '</p>';
        }
        
        resultDiv.innerHTML = html;
    }
    </script>
    
    <div class="box">
        <h2>5. Quick Actions</h2>
        <p>
            <a href="dashboard.php" target="_blank">→ Open Dashboard</a> |
            <a href="test_simple.php">→ Test Simple</a> |
            <a href="ajax/get_dashboard_data.php" target="_blank">→ Direct API</a>
        </p>
    </div>
    
</body>
</html>
