<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level As<PERSON><PERSON>
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

// Get session variables
$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

// Get tahun akreditasi
$tahun_query = "SELECT nama_tahun FROM mapping_paud_visitasi_tahun WHERE provinsi_id = '$provinsi_id'";
$tahun_result = $conn->query($tahun_query);
$nama_tahun = '';
if ($tahun_result && $tahun_result->num_rows > 0) {
    $tahun_row = $tahun_result->fetch_assoc();
    $nama_tahun = $tahun_row['nama_tahun'];
}

// Main query dengan struktur tabel yang benar
$sql = "SELECT
            mv.id_mapping, mv.tahap, mv.tgl_mulai_visitasi, mv.tgl_akhir_visitasi,
            mv.no_surat, mv.tgl_surat,
            s.sekolah_id, s.npsn, s.nama_sekolah, s.nama_kepsek, s.no_hp_kepsek,
            j.nm_jenjang, kk.nm_kota,
            mv.kd_asesor1, mv.kd_asesor2,
            a1.nia1, a1.nm_asesor1, kk1.nm_kota as kota1,
            a2.nia2, a2.nm_asesor2, kk2.nm_kota as kota2,
            mv.file_pakta_integritas_1, mv.file_pakta_integritas_2,
            mv.file_berita_acara_visitasi, mv.file_temuan_hasil_visitasi,
            mv.file_absen_pembuka, mv.file_absen_penutup, mv.file_foto_visitasi,
            mv.tgl_file_foto_visitasi, mv.jam_file_foto_visitasi,
            mv.file_laporan_individu_1, mv.file_laporan_individu_2,
            mv.file_laporan_kelompok, mv.file_penjelasan_hasil_akreditasi
        FROM mapping_paud_visitasi mv
        LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
        LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
        LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
        LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
        LEFT JOIN kab_kota kk1 ON a1.kota_id1 = kk1.kota_id
        LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
        LEFT JOIN kab_kota kk2 ON a2.kota_id2 = kk2.kota_id
        WHERE (mv.kd_asesor1 = '$kd_user' OR mv.kd_asesor2 = '$kd_user')
            AND mv.tahun_akreditasi = '$nama_tahun'
            AND mv.provinsi_id = '$provinsi_id'
        ORDER BY mv.id_mapping DESC";

$result = $conn->query($sql);
?>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Mapping Visitasi PAUD</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Home</a></li>
                        <li class="breadcrumb-item active">Mapping Visitasi PAUD</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-clipboard-check mr-2"></i>
                                Data Mapping Visitasi PAUD - Tahun <?php echo $nama_tahun; ?>
                            </h3>
                            <div class="card-tools">
                                <span class="badge badge-info">
                                    Total: <?php echo $result ? $result->num_rows : 0; ?> data
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <?php if ($result && $result->num_rows > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped table-hover">
                                        <thead class="thead-dark">
                                            <tr>
                                                <th width="3%">NO</th>
                                                <th width="20%">SEKOLAH</th>
                                                <th width="20%">ASESOR VISITASI</th>
                                                <th width="15%">FORM UPLOAD DOKUMEN</th>
                                                <th width="25%">DOKUMEN UNGGAHAN</th>
                                                <th width="17%">JADWAL DAN AKSI</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            while ($row = $result->fetch_assoc()): 
                                                $asesor1 = $row['kd_asesor1'];
                                                $asesor2 = $row['kd_asesor2'];
                                                
                                                // Format tanggal
                                                $tgl_visitasi = '';
                                                if ($row['tgl_mulai_visitasi']) {
                                                    $tgl_visitasi = date('d-m-Y', strtotime($row['tgl_mulai_visitasi']));
                                                }
                                            ?>
                                            <tr>
                                                <td class="text-center"><?php echo $no++; ?></td>
                                                
                                                <!-- SEKOLAH -->
                                                <td>
                                                    <div class="mb-1">
                                                        <strong>NPSN:</strong> <?php echo htmlspecialchars($row['npsn'] ?? '-'); ?>
                                                    </div>
                                                    <div class="mb-1">
                                                        <strong>NAMA:</strong> <?php echo htmlspecialchars($row['nama_sekolah'] ?? '-'); ?>
                                                    </div>
                                                    <div class="mb-1">
                                                        <strong>JENJANG:</strong> <?php echo htmlspecialchars($row['nm_jenjang'] ?? '-'); ?>
                                                    </div>
                                                    <div class="mb-1">
                                                        <strong>KAB/KOTA:</strong> <?php echo htmlspecialchars($row['nm_kota'] ?? '-'); ?>
                                                    </div>
                                                    <div class="mb-1">
                                                        <strong>NAMA KEPSEK:</strong> <?php echo htmlspecialchars($row['nama_kepsek'] ?? '-'); ?>
                                                    </div>
                                                    <div class="mb-1">
                                                        <strong>NO HP KEPSEK:</strong> <?php echo htmlspecialchars($row['no_hp_kepsek'] ?? '-'); ?>
                                                    </div>
                                                    <div>
                                                        <strong>TAHAP VISITASI:</strong> 
                                                        <span class="badge badge-primary"><?php echo htmlspecialchars($row['tahap'] ?? '-'); ?></span>
                                                    </div>
                                                </td>
                                                
                                                <!-- ASESOR VISITASI -->
                                                <td>
                                                    <div class="mb-3">
                                                        <div class="text-center mb-2">
                                                            <strong class="badge badge-info">ASESOR A</strong>
                                                        </div>
                                                        <div class="mb-1">
                                                            <strong>NIA:</strong> <?php echo htmlspecialchars($row['nia1'] ?? '-'); ?>
                                                        </div>
                                                        <div class="mb-1">
                                                            <strong>NAMA:</strong> <?php echo htmlspecialchars($row['nm_asesor1'] ?? '-'); ?>
                                                        </div>
                                                        <div>
                                                            <strong>KAB/KOTA:</strong> <?php echo htmlspecialchars($row['kota1'] ?? '-'); ?>
                                                        </div>
                                                    </div>
                                                    
                                                    <div>
                                                        <div class="text-center mb-2">
                                                            <strong class="badge badge-warning">ASESOR B</strong>
                                                        </div>
                                                        <div class="mb-1">
                                                            <strong>NIA:</strong> <?php echo htmlspecialchars($row['nia2'] ?? '-'); ?>
                                                        </div>
                                                        <div class="mb-1">
                                                            <strong>NAMA:</strong> <?php echo htmlspecialchars($row['nm_asesor2'] ?? '-'); ?>
                                                        </div>
                                                        <div>
                                                            <strong>KAB/KOTA:</strong> <?php echo htmlspecialchars($row['kota2'] ?? '-'); ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                
                                                <!-- FORM UPLOAD DOKUMEN -->
                                                <td class="text-center">
                                                    <?php if ($kd_user == $asesor1): ?>
                                                        <button type="button" class="btn btn-primary btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'pakta_integritas_1')">
                                                            <i class="fas fa-upload mr-1"></i>Pakta Integritas A
                                                        </button><br>
                                                        <button type="button" class="btn btn-success btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'berita_acara_visitasi')">
                                                            <i class="fas fa-upload mr-1"></i>Berita Acara
                                                        </button><br>
                                                        <button type="button" class="btn btn-info btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'temuan_hasil_visitasi')">
                                                            <i class="fas fa-upload mr-1"></i>Temuan Hasil
                                                        </button><br>
                                                        <button type="button" class="btn btn-warning btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'absen_pembuka')">
                                                            <i class="fas fa-upload mr-1"></i>Absen Pembuka
                                                        </button><br>
                                                        <button type="button" class="btn btn-secondary btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'absen_penutup')">
                                                            <i class="fas fa-upload mr-1"></i>Absen Penutup
                                                        </button><br>
                                                        <button type="button" class="btn btn-dark btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'foto_visitasi')">
                                                            <i class="fas fa-upload mr-1"></i>Foto Visitasi
                                                        </button><br>
                                                        <button type="button" class="btn btn-primary btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'laporan_individu_1')">
                                                            <i class="fas fa-upload mr-1"></i>Laporan Individu A
                                                        </button><br>
                                                        <button type="button" class="btn btn-success btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'laporan_kelompok')">
                                                            <i class="fas fa-upload mr-1"></i>Laporan Kelompok
                                                        </button><br>
                                                        <button type="button" class="btn btn-info btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'penjelasan_hasil_akreditasi')">
                                                            <i class="fas fa-upload mr-1"></i>PHA
                                                        </button><br>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($kd_user == $asesor2): ?>
                                                        <button type="button" class="btn btn-warning btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'pakta_integritas_2')">
                                                            <i class="fas fa-upload mr-1"></i>Pakta Integritas B
                                                        </button><br>
                                                        <button type="button" class="btn btn-primary btn-sm mb-1" onclick="uploadFile(<?php echo $row['id_mapping']; ?>, 'laporan_individu_2')">
                                                            <i class="fas fa-upload mr-1"></i>Laporan Individu B
                                                        </button><br>
                                                    <?php endif; ?>
                                                </td>
                                                
                                                <!-- DOKUMEN UNGGAHAN -->
                                                <td style="font-size: 11px;">
                                                    <?php
                                                    $files = [
                                                        'file_pakta_integritas_1' => 'File Pakta Integritas Asesor A',
                                                        'file_pakta_integritas_2' => 'File Pakta Integritas Asesor B',
                                                        'file_berita_acara_visitasi' => 'File Berita Acara Visitasi',
                                                        'file_temuan_hasil_visitasi' => 'File Temuan Hasil Visitasi',
                                                        'file_absen_pembuka' => 'File Absen Pembuka',
                                                        'file_absen_penutup' => 'File Absen Penutup',
                                                        'file_foto_visitasi' => 'File Foto Visitasi',
                                                        'file_laporan_individu_1' => 'File Laporan Individu A',
                                                        'file_laporan_individu_2' => 'File Laporan Individu B',
                                                        'file_laporan_kelompok' => 'File Laporan Kelompok',
                                                        'file_penjelasan_hasil_akreditasi' => 'File Penjelasan Hasil Akreditasi (PHA)'
                                                    ];
                                                    
                                                    foreach ($files as $field => $label):
                                                        $status = !empty($row[$field]) ? 'Sudah Upload' : 'Belum Upload';
                                                        $badge_class = !empty($row[$field]) ? 'badge-success' : 'badge-danger';
                                                        $onclick = !empty($row[$field]) ? "onclick=\"previewFile('{$row[$field]}')\" style=\"cursor: pointer;\"" : '';
                                                    ?>
                                                    <div class="mb-1 d-flex justify-content-between align-items-center">
                                                        <small><strong><?php echo $label; ?>:</strong></small>
                                                        <span class="badge <?php echo $badge_class; ?> badge-sm" <?php echo $onclick; ?>>
                                                            <?php if (!empty($row[$field])): ?>
                                                                <i class="fas fa-eye mr-1"></i>
                                                            <?php endif; ?>
                                                            <?php echo $status; ?>
                                                        </span>
                                                    </div>
                                                    <?php endforeach; ?>
                                                </td>
                                                
                                                <!-- JADWAL DAN AKSI -->
                                                <td class="text-center">
                                                    <div class="mb-2">
                                                        <strong>Tanggal Visitasi:</strong><br>
                                                        <span class="text-info"><?php echo $tgl_visitasi ?: '-'; ?></span>
                                                    </div>
                                                    
                                                    <?php if ($kd_user == $asesor1): ?>
                                                        <button type="button" class="btn btn-outline-primary btn-sm mb-1" onclick="inputTanggal(<?php echo $row['id_mapping']; ?>)">
                                                            <i class="fas fa-calendar mr-1"></i>Input Tanggal
                                                        </button><br>
                                                        <button type="button" class="btn btn-outline-success btn-sm mb-1" onclick="downloadSuratTugas(<?php echo $row['id_mapping']; ?>)">
                                                            <i class="fas fa-download mr-1"></i>Download Surat
                                                        </button><br>
                                                        <button type="button" class="btn btn-outline-info btn-sm" onclick="uploadSuratTugas(<?php echo $row['id_mapping']; ?>)">
                                                            <i class="fas fa-upload mr-1"></i>Upload File
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Tidak ada data mapping visitasi PAUD</h5>
                                    <p class="text-muted">Belum ada data mapping untuk asesor ini.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Upload File -->
<div class="modal fade" id="uploadModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="uploadModalLabel">
                    <i class="fas fa-upload mr-2"></i>Upload File
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="id_mapping" name="id_mapping" value="">
                    <input type="hidden" id="file_type" name="file_type" value="">

                    <div class="form-group">
                        <label for="file_visitasi" id="fileLabel">
                            <i class="fas fa-file-pdf mr-1"></i>Upload File PDF
                        </label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="file_visitasi" name="file_visitasi" accept=".pdf" required>
                            <label class="custom-file-label" for="file_visitasi">Pilih file PDF...</label>
                        </div>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle mr-1"></i>Format file yang diizinkan: PDF
                        </small>
                    </div>

                    <div class="progress mb-3" id="uploadProgress" style="display: none;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%" id="progressBar">
                            <span id="progressText">0%</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>Batal
                    </button>
                    <button type="button" class="btn btn-primary" id="uploadBtn" onclick="handleFileUpload()">
                        <i class="fas fa-upload mr-1"></i>Upload File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Input Tanggal -->
<div class="modal fade" id="tanggalModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-calendar mr-2"></i>Input Tanggal Visitasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="tanggalForm">
                <div class="modal-body">
                    <input type="hidden" id="tanggal_id_mapping" name="id_mapping" value="">

                    <div class="form-group">
                        <label for="tgl_visitasi">
                            <i class="fas fa-calendar-alt mr-1"></i>Tanggal Mulai Visitasi
                        </label>
                        <input type="date" class="form-control" id="tgl_visitasi" name="tgl_visitasi" required>
                        <small class="form-text text-muted">
                            Format: dd-mm-yyyy
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-info" onclick="saveTanggal()">
                        <i class="fas fa-save mr-1"></i>Simpan Tanggal
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Custom styles */
.badge-sm {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.table td {
    vertical-align: middle;
}

.table th {
    background-color: #343a40;
    color: white;
    border-color: #454d55;
}

.badge[onclick] {
    transition: all 0.3s ease;
    cursor: pointer;
}

.badge[onclick]:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.progress {
    height: 25px;
}

.progress-bar {
    font-size: 14px;
    line-height: 25px;
}

#progressText {
    font-weight: bold;
}

/* Professional animations */
.badge {
    transition: all 0.3s ease;
}

.badge-success {
    animation: fadeInSuccess 0.5s ease-in-out;
}

@keyframes fadeInSuccess {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.text-info {
    transition: all 0.3s ease;
}

.text-info.updated {
    animation: highlightUpdate 1s ease-in-out;
}

@keyframes highlightUpdate {
    0% {
        background-color: #fff3cd;
        transform: scale(1);
    }
    50% {
        background-color: #ffeaa7;
        transform: scale(1.05);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

/* Loading states */
.btn.loading {
    position: relative;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* File input label styling */
.custom-file-label {
    cursor: pointer;
    transition: all 0.3s ease;
}

.custom-file-label.file-selected {
    color: #28a745;
    font-weight: 500;
    border-color: #28a745;
}

.custom-file-label:hover {
    border-color: #007bff;
}

/* Debug styling untuk file input */
.custom-file-input:focus ~ .custom-file-label {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Custom JavaScript -->
<script src="js/mapping_visitasi.js"></script>

<!-- Include footer -->
<?php include '../footer.php'; ?>
