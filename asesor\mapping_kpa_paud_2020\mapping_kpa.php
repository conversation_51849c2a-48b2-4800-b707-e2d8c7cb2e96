<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Asesor
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

// Get session variables
$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

// Get tahun akreditasi
$tahun_query = "SELECT nama_tahun FROM mapping_paud_kpa_tahun WHERE provinsi_id = '$provinsi_id'";
$tahun_result = $conn->query($tahun_query);
$nama_tahun = '';
if ($tahun_result && $tahun_result->num_rows > 0) {
    $tahun_row = $tahun_result->fetch_assoc();
    $nama_tahun = $tahun_row['nama_tahun'];
}

// Pagination settings
$limit = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $limit;

// Count total records
$count_query = "SELECT COUNT(*) as total
                FROM mapping_paud_kpa m
                LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
                LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
                WHERE a.kd_asesor = ?
                    AND s.rumpun = 'paud'
                    AND m.provinsi_id = ?
                    AND m.tahun_akreditasi = ?";

$count_stmt = $conn->prepare($count_query);
$count_stmt->bind_param("sis", $kd_user, $provinsi_id, $nama_tahun);
$count_stmt->execute();
$count_result = $count_stmt->get_result();
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $limit);

// Main query with pagination
$query = "SELECT 
            m.id_mapping, 
            m.sekolah_id, 
            s.npsn, 
            s.nama_sekolah, 
            s.nama_kepsek, 
            s.no_hp_kepsek, 
            j.nm_jenjang, 
            ks.nm_kota as kota_sekolah,
            a.nia, 
            a.nm_asesor, 
            ka.nm_kota as kota_asesor,
            m.tahap, 
            m.tgl_penetapan_kpa,
            m.file_laporan_hasil_kpa,
            m.tahun_akreditasi
          FROM mapping_paud_kpa m
          LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
          LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
          LEFT JOIN kab_kota ks ON s.kota_id = ks.kota_id
          LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
          LEFT JOIN kab_kota ka ON a.kota_id = ka.kota_id
          WHERE a.kd_asesor = ?
              AND s.rumpun = 'paud'
              AND m.provinsi_id = ?
              AND m.tahun_akreditasi = ?
          ORDER BY m.id_mapping DESC
          LIMIT ? OFFSET ?";

$stmt = $conn->prepare($query);
$stmt->bind_param("sisii", $kd_user, $provinsi_id, $nama_tahun, $limit, $offset);
$stmt->execute();
$result = $stmt->get_result();
?>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Mapping KPA PAUD</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Home</a></li>
                        <li class="breadcrumb-item active">Mapping KPA PAUD</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-users mr-2"></i>
                                Data Mapping KPA PAUD
                            </h3>
                            <div class="card-tools">
                                <span class="badge badge-info">
                                    Total: <?php echo $total_records; ?> data
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <?php if ($total_records > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped table-hover">
                                        <thead class="thead-dark">
                                            <tr>
                                                <th width="3%">NO</th>
                                                <th width="25%">SEKOLAH</th>
                                                <th width="15%">ASESOR KPA</th>
                                                <th width="12%">DOKUMEN UNGGAHAN</th>
                                                <th width="8%">PREVIEW</th>
                                                <th width="15%">PELAKSANAAN KEGIATAN</th>
                                                <th width="5%">TAHAP</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = $offset + 1;
                                            while ($row = $result->fetch_assoc()): 
                                                // Format tanggal
                                                $tgl_penetapan = '';
                                                if ($row['tgl_penetapan_kpa']) {
                                                    $tgl_penetapan = date('d-m-Y', strtotime($row['tgl_penetapan_kpa']));
                                                }
                                                
                                                // Status upload
                                                $upload_status = '';
                                                $upload_class = '';
                                                if (!empty($row['file_laporan_hasil_kpa'])) {
                                                    $upload_status = 'Sudah Upload';
                                                    $upload_class = 'badge-success';
                                                } else {
                                                    $upload_status = 'Belum Upload';
                                                    $upload_class = 'badge-danger';
                                                }
                                            ?>
                                            <tr>
                                                <td class="text-center"><?php echo $no++; ?></td>
                                                
                                                <!-- SEKOLAH -->
                                                <td>
                                                    <div class="mb-1">
                                                        <strong>NPSN:</strong> <?php echo htmlspecialchars($row['npsn'] ?? '-'); ?>
                                                    </div>
                                                    <div class="mb-1">
                                                        <strong>NAMA:</strong> <?php echo htmlspecialchars($row['nama_sekolah'] ?? '-'); ?>
                                                    </div>
                                                    <div class="mb-1">
                                                        <strong>JENJANG:</strong> <?php echo htmlspecialchars($row['nm_jenjang'] ?? '-'); ?>
                                                    </div>
                                                    <div class="mb-1">
                                                        <strong>KAB/KOTA:</strong> <?php echo htmlspecialchars($row['kota_sekolah'] ?? '-'); ?>
                                                    </div>
                                                    <div class="mb-1">
                                                        <strong>NAMA KEPSEK:</strong> <?php echo htmlspecialchars($row['nama_kepsek'] ?? '-'); ?>
                                                    </div>
                                                    <div>
                                                        <strong>NO HP KEPSEK:</strong> <?php echo htmlspecialchars($row['no_hp_kepsek'] ?? '-'); ?>
                                                    </div>
                                                </td>
                                                
                                                <!-- ASESOR KPA -->
                                                <td>
                                                    <div class="mb-1">
                                                        <strong>NIA:</strong> <?php echo htmlspecialchars($row['nia'] ?? '-'); ?>
                                                    </div>
                                                    <div class="mb-1">
                                                        <strong>NAMA:</strong> <?php echo htmlspecialchars($row['nm_asesor'] ?? '-'); ?>
                                                    </div>
                                                    <div>
                                                        <strong>KAB/KOTA:</strong> <?php echo htmlspecialchars($row['kota_asesor'] ?? '-'); ?>
                                                    </div>
                                                </td>
                                                
                                                <!-- DOKUMEN UNGGAHAN -->
                                                <td class="text-center">
                                                    <div class="mb-2">
                                                        <small class="text-muted">File Hasil KPA:</small>
                                                    </div>
                                                    <button type="button" class="btn btn-primary btn-sm" 
                                                            onclick="uploadFile(<?php echo $row['id_mapping']; ?>)">
                                                        <i class="fas fa-upload mr-1"></i>
                                                        Unggah File
                                                    </button>
                                                </td>
                                                
                                                <!-- PREVIEW -->
                                                <td class="text-center">
                                                    <?php if (!empty($row['file_laporan_hasil_kpa'])): ?>
                                                        <span class="badge <?php echo $upload_class; ?> p-2"
                                                              style="cursor: pointer;"
                                                              onclick="previewFile('<?php echo htmlspecialchars($row['file_laporan_hasil_kpa']); ?>')"
                                                              title="Klik untuk melihat file">
                                                            <i class="fas fa-eye mr-1"></i><?php echo $upload_status; ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge <?php echo $upload_class; ?> p-2">
                                                            <?php echo $upload_status; ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                
                                                <!-- PELAKSANAAN KEGIATAN -->
                                                <td>
                                                    <strong>Tanggal Penetapan KPA PAUD:</strong><br>
                                                    <span class="text-info">
                                                        <?php echo $tgl_penetapan ?: '-'; ?>
                                                    </span>
                                                </td>
                                                
                                                <!-- TAHAP -->
                                                <td class="text-center">
                                                    <span class="badge badge-info badge-lg">
                                                        <?php echo htmlspecialchars($row['tahap'] ?? '-'); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                <?php if ($total_pages > 1): ?>
                                <div class="row mt-3">
                                    <div class="col-sm-12 col-md-5">
                                        <div class="dataTables_info">
                                            Menampilkan <?php echo $offset + 1; ?> sampai <?php echo min($offset + $limit, $total_records); ?> 
                                            dari <?php echo $total_records; ?> data
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-md-7">
                                        <div class="dataTables_paginate paging_simple_numbers float-right">
                                            <ul class="pagination">
                                                <!-- Previous -->
                                                <li class="paginate_button page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                                    <a href="<?php echo ($page > 1) ? '?page=' . ($page - 1) : '#'; ?>" 
                                                       class="page-link">Previous</a>
                                                </li>
                                                
                                                <!-- Page Numbers -->
                                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                                <li class="paginate_button page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                                    <a href="?page=<?php echo $i; ?>" class="page-link"><?php echo $i; ?></a>
                                                </li>
                                                <?php endfor; ?>
                                                
                                                <!-- Next -->
                                                <li class="paginate_button page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                                    <a href="<?php echo ($page < $total_pages) ? '?page=' . ($page + 1) : '#'; ?>" 
                                                       class="page-link">Next</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Tidak ada data mapping KPA PAUD</h5>
                                    <p class="text-muted">Belum ada data mapping untuk asesor ini.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Upload File -->
<div class="modal fade" id="uploadModal" tabindex="-1" role="dialog" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadModalLabel">
                    <i class="fas fa-upload mr-2"></i>Upload File Hasil KPA
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <!-- Hidden fields -->
                    <input type="hidden" id="id_mapping" name="id_mapping" value="">

                    <!-- File input -->
                    <div class="form-group">
                        <label for="file_laporan_hasil_kpa">
                            <i class="fas fa-file-pdf mr-1"></i>Upload File Laporan Hasil KPA
                        </label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="file_laporan_hasil_kpa" name="file_laporan_hasil_kpa" accept=".pdf" required>
                            <label class="custom-file-label" for="file_laporan_hasil_kpa">Pilih file PDF...</label>
                        </div>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle mr-1"></i>Format file yang diizinkan: PDF
                        </small>
                    </div>

                    <!-- Progress bar -->
                    <div class="progress mb-3" id="uploadProgress" style="display: none;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%" id="progressBar">
                            <span id="progressText">0%</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>Batal
                    </button>
                    <button type="button" class="btn btn-primary" id="uploadBtn" onclick="handleFileUpload()">
                        <i class="fas fa-upload mr-1"></i>Upload File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Custom JavaScript -->
<script src="js/mapping_kpa.js"></script>

<style>
/* Custom styles for mapping KPA */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    color: white;
    font-size: 2rem;
}

.badge-lg {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

.table td {
    vertical-align: middle;
}

.table th {
    background-color: #343a40;
    color: white;
    border-color: #454d55;
}

.pagination .page-link {
    color: #007bff;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* Upload Modal Styles */
.modal-header {
    background-color: #007bff;
    color: white;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
}

.modal-header .close:hover {
    opacity: 1;
}

.custom-file-label::after {
    content: "Browse";
}

.progress {
    height: 25px;
}

.progress-bar {
    font-size: 14px;
    line-height: 25px;
}

#progressText {
    font-weight: bold;
}

/* Preview File Styles */
.badge[onclick] {
    transition: all 0.3s ease;
}

.badge[onclick]:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.badge-success[onclick]:hover {
    background-color: #1e7e34 !important;
}
</style>

<!-- Include footer -->
<?php include '../footer.php'; ?>
