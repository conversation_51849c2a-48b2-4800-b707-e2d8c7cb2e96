<?php
/**
 * AJAX handler untuk update tanggal validasi
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi parameter
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID mapping tidak valid');
    }
    
    if (!isset($_POST['tgl_mulai_validasi']) || !isset($_POST['tgl_akhir_validasi'])) {
        throw new Exception('Data tanggal tidak lengkap');
    }
    
    $id_mapping = intval($_POST['id_mapping']);
    $tgl_mulai = $_POST['tgl_mulai_validasi'];
    $tgl_akhir = $_POST['tgl_akhir_validasi'];
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Validasi format tanggal
    if (!empty($tgl_mulai) && !DateTime::createFromFormat('Y-m-d', $tgl_mulai)) {
        throw new Exception('Format tanggal mulai tidak valid');
    }
    
    if (!empty($tgl_akhir) && !DateTime::createFromFormat('Y-m-d', $tgl_akhir)) {
        throw new Exception('Format tanggal akhir tidak valid');
    }
    
    // Convert empty string to NULL untuk database
    $tgl_mulai = empty($tgl_mulai) ? null : $tgl_mulai;
    $tgl_akhir = empty($tgl_akhir) ? null : $tgl_akhir;
    
    // Cek apakah mapping exists dan milik provinsi user
    $check_sql = "SELECT mv.id_mapping 
                  FROM mapping_validasi_2024 mv
                  LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
                  WHERE mv.id_mapping = ? 
                  AND mv.provinsi_id = ?
                  AND s.rumpun = 'dasmen'";
    
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id_session);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows == 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    // Update tanggal validasi
    $update_sql = "UPDATE mapping_validasi_2024 
                   SET tgl_mulai_validasi = ?, 
                       tgl_akhir_validasi = ?
                   WHERE id_mapping = ? 
                   AND provinsi_id = ?";
    
    $update_stmt = $conn->prepare($update_sql);
    $update_stmt->bind_param("ssii", $tgl_mulai, $tgl_akhir, $id_mapping, $provinsi_id_session);
    
    if ($update_stmt->execute()) {
        // Format tanggal untuk response (dd/mm/yyyy)
        function formatTanggalResponse($tanggal) {
            if (empty($tanggal) || $tanggal == '0000-00-00') {
                return '-';
            }
            return date('d/m/Y', strtotime($tanggal));
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Tanggal validasi berhasil diupdate',
            'data' => [
                'tgl_mulai' => formatTanggalResponse($tgl_mulai),
                'tgl_akhir' => formatTanggalResponse($tgl_akhir),
                'tgl_mulai_raw' => $tgl_mulai,
                'tgl_akhir_raw' => $tgl_akhir
            ]
        ]);
    } else {
        throw new Exception('Gagal update tanggal validasi: ' . $conn->error);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
    error_log("Error in update_tanggal_validasi.php: " . $e->getMessage());
}
?>
