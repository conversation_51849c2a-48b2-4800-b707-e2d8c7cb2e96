<?php
/**
 * AJAX handler untuk mengambil detail mapping validasi SM
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi parameter id_mapping
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID mapping tidak valid');
    }
    
    $id_mapping = intval($_POST['id_mapping']);
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mengambil detail lengkap mapping validasi
    $sql = "SELECT 
                mv.id_mapping,
                mv.sekolah_id,
                mv.kd_asesor1,
                mv.kd_asesor2,
                mv.tahun_akreditasi,
                mv.tahap,
                mv.tgl_mulai_validasi,
                mv.tgl_akhir_validasi,
                mv.no_surat_validasi,
                mv.tgl_surat_validasi,
                mv.file_format_5_1_berita_acara_hasil_validasi_1,
                mv.file_format_5_1_berita_acara_hasil_validasi_2,
                mv.file_pakta_integritas_1,
                mv.file_pakta_integritas_2,
                mv.file_st_validasi,
                
                -- Data Sekolah
                s.npsn,
                s.nama_sekolah,
                s.nama_kepsek,
                s.no_hp_kepsek,
                s.no_wa_kepsek,
                
                -- Data Jenjang dan Kab/Kota Sekolah
                j.nm_jenjang,
                k1.nm_kota as kota_sekolah,
                
                -- Data Asesor 1
                a1.nia1,
                a1.nm_asesor1,
                a1.no_hp as hp_asesor1,
                k2.nm_kota as kota_asesor1,
                
                -- Data Asesor 2
                a2.nia2,
                a2.nm_asesor2,
                a2.no_hp as hp_asesor2,
                k3.nm_kota as kota_asesor2
                
            FROM mapping_validasi_2024 mv
            LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
            LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
            LEFT JOIN kab_kota k1 ON s.kota_id = k1.kota_id
            LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
            LEFT JOIN kab_kota k2 ON a1.kota_id1 = k2.kota_id
            LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
            LEFT JOIN kab_kota k3 ON a2.kota_id2 = k3.kota_id
            WHERE mv.id_mapping = ? 
            AND mv.provinsi_id = ?
            AND s.rumpun = 'kesetaraan'";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $id_mapping, $provinsi_id_session);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $data = $result->fetch_assoc();
    
    // Format tanggal ke dd/mm/yyyy
    function formatTanggal($tanggal) {
        if (empty($tanggal) || $tanggal == '0000-00-00') {
            return '-';
        }
        return date('d/m/Y', strtotime($tanggal));
    }
    
    // Format status file upload dengan link langsung
    function formatStatusFile($file, $fileType) {
        if (!empty($file)) {
            // Tentukan path berdasarkan jenis file
            $basePath = '../../../simak/files/';
            switch($fileType) {
                case 'ba1':
                    $path = $basePath . 'upload_file_format_5_1_berita_acara_hasil_validasi_1/';
                    break;
                case 'ba2':
                    $path = $basePath . 'upload_file_format_5_1_berita_acara_hasil_validasi_2/';
                    break;
                case 'pakta1':
                    $path = $basePath . 'upload_file_pakta_integritas_validasi_1/';
                    break;
                case 'pakta2':
                    $path = $basePath . 'upload_file_pakta_integritas_validasi_2/';
                    break;
                case 'st':
                    $path = $basePath . 'upload_file_st_validasi/';
                    break;
                default:
                    $path = $basePath;
            }

            $fullPath = $path . $file;
            return '<a href="' . $fullPath . '" target="_blank" class="badge badge-success text-white text-decoration-none" style="cursor: pointer;" title="Klik untuk membuka file">Sudah Upload</a>';
        } else {
            return '<span class="badge badge-danger text-white" title="File belum diupload">Belum Upload</span>';
        }
    }
    
    // Prepare response data
    $response_data = [
        'id_mapping' => $data['id_mapping'],
        
        // Data Sekolah
        'npsn' => $data['npsn'] ?? '-',
        'nama_sekolah' => $data['nama_sekolah'] ?? '-',
        'jenjang' => $data['nm_jenjang'] ?? '-',
        'kab_kota' => $data['kota_sekolah'] ?? '-',
        'nama_kepsek' => $data['nama_kepsek'] ?? '-',
        'hp_kepsek' => $data['no_hp_kepsek'] ?? '-',
        'wa_kepsek' => $data['no_wa_kepsek'] ?? '-',
        
        // Data Asesor 1
        'nia1' => $data['nia1'] ?? '-',
        'nama_asesor1' => $data['nm_asesor1'] ?? '-',
        'hp_asesor1' => $data['hp_asesor1'] ?? '-',
        'kota_asesor1' => $data['kota_asesor1'] ?? '-',
        
        // Data Asesor 2
        'nia2' => $data['nia2'] ?? '-',
        'nama_asesor2' => $data['nm_asesor2'] ?? '-',
        'hp_asesor2' => $data['hp_asesor2'] ?? '-',
        'kota_asesor2' => $data['kota_asesor2'] ?? '-',
        
        // Dokumen Unggahan
        'file_ba1' => formatStatusFile($data['file_format_5_1_berita_acara_hasil_validasi_1'], 'ba1'),
        'file_ba2' => formatStatusFile($data['file_format_5_1_berita_acara_hasil_validasi_2'], 'ba2'),
        'file_pakta1' => formatStatusFile($data['file_pakta_integritas_1'], 'pakta1'),
        'file_pakta2' => formatStatusFile($data['file_pakta_integritas_2'], 'pakta2'),
        'file_st' => formatStatusFile($data['file_st_validasi'], 'st'),
        
        // Pelaksanaan Kegiatan
        'tgl_mulai' => formatTanggal($data['tgl_mulai_validasi']),
        'tgl_akhir' => formatTanggal($data['tgl_akhir_validasi']),
        'no_st' => $data['no_surat_validasi'] ?? '-',
        'tgl_st' => formatTanggal($data['tgl_surat_validasi']),
        'tahap' => $data['tahap'] ?? '-',

        // Raw tanggal untuk form edit
        'tgl_mulai_raw' => $data['tgl_mulai_validasi'] ?? '',
        'tgl_akhir_raw' => $data['tgl_akhir_validasi'] ?? ''
    ];
    
    echo json_encode([
        'success' => true,
        'data' => $response_data
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
    error_log("Error in get_detail_validasi.php: " . $e->getMessage());
}
?>
