<?php
require_once '../../koneksi.php';
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

$query = "SELECT m.id_mapping, s.nama_sekolah
          FROM mapping_paud_kpa m
          LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
          LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
          WHERE a.kd_asesor = '$kd_user' AND m.provinsi_id = '$provinsi_id'
          LIMIT 1";

$result = mysqli_query($conn, $query);
$sample_mapping = mysqli_fetch_assoc($result);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Modal Upload</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h2>🧪 Test Modal Upload</h2>
        
        <?php if ($sample_mapping): ?>
            <p><strong>Test Mapping ID:</strong> <?php echo $sample_mapping['id_mapping']; ?></p>
            <p><strong>Sekolah:</strong> <?php echo $sample_mapping['nama_sekolah']; ?></p>
            
            <button type="button" class="btn btn-primary" onclick="uploadFile(<?php echo $sample_mapping['id_mapping']; ?>)">
                <i class="fas fa-upload mr-1"></i>Test Upload Modal
            </button>
            
        <?php else: ?>
            <p class="text-danger">No mapping data found</p>
        <?php endif; ?>
    </div>

    <!-- Modal Upload File -->
    <div class="modal fade" id="uploadModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-upload mr-2"></i>Upload File Hasil KPA
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" id="id_mapping" name="id_mapping" value="">
                        
                        <div class="form-group">
                            <label for="file_laporan_hasil_kpa">
                                <i class="fas fa-file-pdf mr-1"></i>Upload File Laporan Hasil KPA
                            </label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="file_laporan_hasil_kpa" name="file_laporan_hasil_kpa" accept=".pdf" required>
                                <label class="custom-file-label" for="file_laporan_hasil_kpa">Pilih file PDF...</label>
                            </div>
                        </div>
                        
                        <div class="progress mb-3" id="uploadProgress" style="display: none;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%" id="progressBar">
                                <span id="progressText">0%</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                        <button type="button" class="btn btn-primary" id="uploadBtn" onclick="handleFileUpload()">
                            <i class="fas fa-upload mr-1"></i>Upload File
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.0/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
    function uploadFile(idMapping) {
        console.log('uploadFile called with ID:', idMapping);
        $('#id_mapping').val(idMapping);
        $('#uploadForm')[0].reset();
        $('#id_mapping').val(idMapping); // Set again after reset
        $('#file_laporan_hasil_kpa').next('.custom-file-label').html('Pilih file PDF...');
        $('#uploadProgress').hide();
        $('#progressBar').css('width', '0%');
        $('#progressText').text('0%');
        $('#uploadBtn').prop('disabled', false).html('<i class="fas fa-upload mr-1"></i>Upload File');
        $('#uploadModal').modal('show');
    }

    function handleFileUpload() {
        console.log('handleFileUpload called');
        
        const formData = new FormData($('#uploadForm')[0]);
        const uploadBtn = $('#uploadBtn');
        const progressContainer = $('#uploadProgress');
        const progressBar = $('#progressBar');
        const progressText = $('#progressText');
        
        // Validate file
        const fileInput = $('#file_laporan_hasil_kpa')[0];
        if (!fileInput.files.length) {
            Swal.fire('Error', 'Silahkan pilih file terlebih dahulu', 'error');
            return;
        }
        
        // Debug
        console.log('ID Mapping:', $('#id_mapping').val());
        console.log('File selected:', fileInput.files[0]);
        
        // Show progress
        progressContainer.show();
        progressBar.css('width', '0%');
        progressText.text('0%');
        uploadBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>Uploading...');
        
        $.ajax({
            url: 'ajax/upload_file.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = Math.round((e.loaded / e.total) * 100);
                        progressBar.css('width', percentComplete + '%');
                        progressText.text(percentComplete + '%');
                    }
                }, false);
                return xhr;
            },
            success: function(response) {
                console.log('Upload response:', response);
                
                if (response.success) {
                    progressBar.removeClass('progress-bar-striped progress-bar-animated').addClass('bg-success');
                    progressText.text('Complete!');
                    
                    Swal.fire('Berhasil!', response.message, 'success');
                    
                    setTimeout(function() {
                        $('#uploadModal').modal('hide');
                    }, 1000);
                    
                } else {
                    progressBar.removeClass('progress-bar-striped progress-bar-animated').addClass('bg-danger');
                    progressText.text('Failed!');
                    
                    Swal.fire('Error!', response.message, 'error');
                    uploadBtn.prop('disabled', false).html('<i class="fas fa-upload mr-1"></i>Upload File');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);
                
                progressBar.removeClass('progress-bar-striped progress-bar-animated').addClass('bg-danger');
                progressText.text('Error!');
                
                Swal.fire('Error!', 'File gagal di-upload, silahkan upload ulang', 'error');
                uploadBtn.prop('disabled', false).html('<i class="fas fa-upload mr-1"></i>Upload File');
            }
        });
    }

    // Handle file input change
    $('#file_laporan_hasil_kpa').on('change', function() {
        const fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName || 'Pilih file PDF...');
    });
    </script>
</body>
</html>
