/**
 * JavaScript untuk Modul Mapping KPA PAUD
 * Version: 1.0.0
 */

$(document).ready(function() {
    // Initialize page
    initializePage();
});

/**
 * Initialize page functionality
 */
function initializePage() {
    // Initialize page functionality
    setupEventListeners();
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Handle pagination clicks
    $('.pagination a').on('click', function(e) {
        $(this).addClass('loading');
    });

    // Handle file input change
    $('#file_laporan_hasil_kpa').on('change', function() {
        const fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName || 'Pilih file PDF...');
    });

    // Handle upload form submit (backup prevention)
    $(document).on('submit', '#uploadForm', function(e) {
        e.preventDefault();
        e.stopPropagation();
        handleFileUpload();
        return false;
    });
}

/**
 * Upload file function - Show modal
 * @param {number} idMapping - ID mapping yang akan diupload
 */
function uploadFile(idMapping) {
    // Set ID mapping ke form
    $('#id_mapping').val(idMapping);

    // Reset form
    $('#uploadForm')[0].reset();

    // Set ID mapping again after reset (important!)
    $('#id_mapping').val(idMapping);

    // Reset file label
    $('#file_laporan_hasil_kpa').next('.custom-file-label').html('Pilih file PDF...');

    // Hide progress bar
    $('#uploadProgress').hide();
    $('#progressBar').css('width', '0%');
    $('#progressText').text('0%');

    // Enable upload button
    $('#uploadBtn').prop('disabled', false).html('<i class="fas fa-upload mr-1"></i>Upload File');

    // Show modal
    $('#uploadModal').modal('show');
}

/**
 * Handle file upload with progress bar
 */
function handleFileUpload() {
    const formData = new FormData($('#uploadForm')[0]);
    const uploadBtn = $('#uploadBtn');
    const progressContainer = $('#uploadProgress');
    const progressBar = $('#progressBar');
    const progressText = $('#progressText');

    // Validate file
    const fileInput = $('#file_laporan_hasil_kpa')[0];
    if (!fileInput.files.length) {
        showError('Silahkan pilih file terlebih dahulu');
        return;
    }

    // Show progress bar
    progressContainer.show();
    progressBar.css('width', '0%');
    progressText.text('0%');

    // Disable upload button
    uploadBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>Uploading...');

    // AJAX upload with progress
    $.ajax({
        url: 'ajax/upload_file.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();

            // Upload progress
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    progressBar.css('width', percentComplete + '%');
                    progressText.text(percentComplete + '%');
                }
            }, false);

            return xhr;
        },
        success: function(response) {
            if (response.success) {
                // Success
                progressBar.removeClass('progress-bar-striped progress-bar-animated')
                          .addClass('bg-success');
                progressText.text('Complete!');

                // Show success message
                showSuccess(response.message);

                // Update UI - change preview status
                updatePreviewStatus(response.data.id_mapping, true);

                // Auto close modal after 1 second
                setTimeout(function() {
                    $('#uploadModal').modal('hide');
                }, 1000);

            } else {
                // Error
                progressBar.removeClass('progress-bar-striped progress-bar-animated')
                          .addClass('bg-danger');
                progressText.text('Failed!');

                showError(response.message);

                // Re-enable upload button
                uploadBtn.prop('disabled', false).html('<i class="fas fa-upload mr-1"></i>Upload File');
            }
        },
        error: function(xhr, status, error) {
            // AJAX Error
            progressBar.removeClass('progress-bar-striped progress-bar-animated')
                      .addClass('bg-danger');
            progressText.text('Error!');

            showError('File gagal di-upload, silahkan upload ulang');

            // Re-enable upload button
            uploadBtn.prop('disabled', false).html('<i class="fas fa-upload mr-1"></i>Upload File');
        }
    });
}

/**
 * Show loading state
 */
function showLoading() {
    // Add loading overlay if needed
    $('body').append('<div id="loading-overlay" class="loading-overlay"><i class="fas fa-spinner fa-spin"></i></div>');
}

/**
 * Hide loading state
 */
function hideLoading() {
    $('#loading-overlay').remove();
}

/**
 * Show success message
 * @param {string} message - Success message
 */
function showSuccess(message) {
    Swal.fire({
        title: 'Berhasil!',
        text: message,
        icon: 'success',
        confirmButtonColor: '#28a745'
    });
}

/**
 * Show error message
 * @param {string} message - Error message
 */
function showError(message) {
    Swal.fire({
        title: 'Error!',
        text: message,
        icon: 'error',
        confirmButtonColor: '#dc3545'
    });
}

/**
 * Update preview status in table
 * @param {number} idMapping - ID mapping
 * @param {boolean} uploaded - Upload status
 */
function updatePreviewStatus(idMapping, uploaded) {
    // Find the row with this ID mapping
    $('button[onclick="uploadFile(' + idMapping + ')"]').closest('tr').find('td:nth-child(5) .badge').each(function() {
        if (uploaded) {
            $(this).removeClass('badge-danger')
                   .addClass('badge-success')
                   .text('Sudah Upload');
        } else {
            $(this).removeClass('badge-success')
                   .addClass('badge-danger')
                   .text('Belum Upload');
        }
    });
}

/**
 * Refresh page data
 */
function refreshData() {
    showLoading();
    location.reload();
}
