<?php
/**
 * AJAX handler untuk autocomplete NPSN sekolah
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Ambil parameter pencarian
    $search = isset($_POST['search']) ? trim($_POST['search']) : '';
    
    if (empty($search)) {
        echo json_encode(['success' => true, 'data' => []]);
        exit;
    }
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mencari sekolah berdasarkan NPSN atau nama sekolah
    // Sesuai provinsi user, tanpa filter rumpun
    $sql = "SELECT s.sekolah_id, s.npsn, s.nama_sekolah, s.rumpun, 
                   j.nm_jenjang, k.nm_kota
            FROM sekolah s
            LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
            LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
            WHERE s.provinsi_id = ? 
            AND s.soft_delete = '1'
            AND s.status_keaktifan_id = '1'
            AND (s.npsn LIKE ? OR s.nama_sekolah LIKE ?)
            ORDER BY s.npsn ASC
            LIMIT 10";
    
    $search_param = "%{$search}%";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iss", $provinsi_id_session, $search_param, $search_param);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            'sekolah_id' => $row['sekolah_id'],
            'npsn' => $row['npsn'],
            'nama_sekolah' => $row['nama_sekolah'],
            'rumpun' => $row['rumpun'],
            'jenjang' => $row['nm_jenjang'],
            'kota' => $row['nm_kota'],
            'label' => $row['npsn'] . ' - ' . $row['nama_sekolah'],
            'value' => $row['npsn']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
        'data' => []
    ]);
    error_log("Error in autocomplete_npsn.php: " . $e->getMessage());
}
?>
