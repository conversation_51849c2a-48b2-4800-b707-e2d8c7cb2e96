# Dashboard SIMAK - KISS Method Implementation

## 🎯 KISS Principle Applied
**Keep It Simple, Stupid** - Implementasi dashboard dengan query sederhana dan langsung berdasarkan struktur tabel yang ada.

## 📊 Query Structure

### 1. **Sekolah per Rumpun**
```sql
-- DASMEN
SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
FROM sekolah s
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
WHERE s.provinsi_id = ? AND s.rumpun = 'dasmen'
GROUP BY j.jenjang_id, j.nm_jenjang
ORDER BY j.jenjang_id

-- PAUD
SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
FROM sekolah s
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
WHERE s.provinsi_id = ? AND s.rumpun = 'paud'
GROUP BY j.jenjang_id, j.nm_jenjang
ORDER BY j.jenjang_id

-- KESETARAAN
SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
FROM sekolah s
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
WHERE s.provinsi_id = ? AND s.rumpun = 'kesetaraan'
GROUP BY j.jenjang_id, j.nm_jenjang
ORDER BY j.jenjang_id
```

### 2. **Akreditasi per Rumpun**
```sql
SELECT s.rumpun, ha.peringkat, COUNT(*) as total
FROM hasil_akreditasi ha
JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
WHERE ha.provinsi_id = ? AND ha.peringkat IN ('A', 'B', 'C')
GROUP BY s.rumpun, ha.peringkat
ORDER BY s.rumpun, ha.peringkat
```

### 3. **Akreditasi per Jenjang**
```sql
SELECT j.nm_jenjang, ha.peringkat, COUNT(*) as total
FROM hasil_akreditasi ha
JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
JOIN jenjang j ON s.jenjang_id = j.jenjang_id
WHERE ha.provinsi_id = ? AND ha.peringkat IN ('A', 'B', 'C')
GROUP BY j.jenjang_id, j.nm_jenjang, ha.peringkat
ORDER BY j.jenjang_id, ha.peringkat
```

### 4. **Akreditasi per Kota**
```sql
SELECT kk.nm_kota, ha.peringkat, COUNT(*) as total
FROM hasil_akreditasi ha
JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
JOIN kab_kota kk ON s.kota_id = kk.kota_id
WHERE ha.provinsi_id = ? AND ha.peringkat IN ('A', 'B', 'C')
GROUP BY kk.kota_id, kk.nm_kota, ha.peringkat
ORDER BY kk.nm_kota, ha.peringkat
LIMIT 30
```

## 🗂️ Table Structure Used

### **sekolah**
- `sekolah_id` (int) - Primary key
- `nama_sekolah` (varchar) - Nama sekolah
- `jenjang_id` (int) - Foreign key ke tabel jenjang
- `rumpun` (varchar) - 'dasmen', 'paud', 'kesetaraan'
- `provinsi_id` (int) - Filter utama
- `kota_id` (int) - Foreign key ke tabel kab_kota

### **jenjang**
- `jenjang_id` (varchar) - Primary key
- `nm_jenjang` (varchar) - Nama jenjang (SD, SMP, TK, dll)

### **hasil_akreditasi**
- `sekolah_id` (int) - Foreign key ke tabel sekolah
- `peringkat` (varchar) - 'A', 'B', 'C'
- `provinsi_id` (int) - Filter utama

### **kab_kota**
- `kota_id` (varchar) - Primary key
- `nm_kota` (varchar) - Nama kota/kabupaten

## 🔧 Implementation Details

### **File Structure**
```
tim_it/dashboard/
├── dashboard.php                 # Main dashboard
├── ajax/get_dashboard_data.php   # KISS API endpoint
├── test_simple.php              # Simple testing tool
└── KISS_METHOD.md               # This documentation
```

### **API Response Format**
```json
{
    "success": true,
    "data": {
        "sekolah_dasmen": [
            {"nm_jenjang": "SD", "total": "25"},
            {"nm_jenjang": "SMP", "total": "15"}
        ],
        "total_dasmen": 40,
        "sekolah_paud": [
            {"nm_jenjang": "TK", "total": "12"}
        ],
        "total_paud": 12,
        "sekolah_kesetaraan": [
            {"nm_jenjang": "Paket A", "total": "3"}
        ],
        "total_kesetaraan": 3,
        "akreditasi_rumpun": [
            {"rumpun": "dasmen", "peringkat": "A", "total": "8"}
        ],
        "akreditasi_jenjang": [
            {"nm_jenjang": "SD", "peringkat": "A", "total": "5"}
        ],
        "akreditasi_kota": [
            {"nm_kota": "Samarinda", "peringkat": "A", "total": "12"}
        ]
    }
}
```

## 🚀 Testing Steps

### **Step 1: Test Individual Queries**
```
http://localhost/app-smkpdm/tim_it/dashboard/test_simple.php
```

### **Step 2: Test API Endpoint**
```
http://localhost/app-smkpdm/tim_it/dashboard/ajax/get_dashboard_data.php
```

### **Step 3: Test Dashboard**
```
http://localhost/app-smkpdm/tim_it/dashboard/dashboard.php
```

## 🎯 Key Simplifications

### **Removed Complex Logic**
- ❌ No complex categorization logic
- ❌ No multiple fallback strategies  
- ❌ No complex error handling
- ❌ No extensive logging

### **Direct Approach**
- ✅ Direct SQL queries based on `rumpun` field
- ✅ Simple JOIN operations
- ✅ Minimal error handling
- ✅ Straightforward response format

### **Fallback Strategy**
- If no real data: Use simple dummy data
- No complex proportional calculations
- Fixed dummy values for demo

## 🔍 Troubleshooting

### **No Data Showing**
1. Check `test_simple.php` first
2. Verify `provinsi_id` in session (default: 64)
3. Check if `rumpun` field has values: 'dasmen', 'paud', 'kesetaraan'
4. Verify table relationships are correct

### **Common Issues**
- **Empty rumpun field**: Data won't be categorized
- **Wrong provinsi_id**: No data will be returned
- **Missing jenjang data**: Will show as NULL
- **No akreditasi data**: Will use dummy data

## 📝 Configuration

### **Default Values**
- `provinsi_id`: 64 (Kalimantan Timur)
- Dummy data used if no real akreditasi data
- No complex filtering (status_keaktifan, soft_delete)

### **Database Requirements**
- Tables: `sekolah`, `jenjang`, `hasil_akreditasi`, `kab_kota`
- Required fields: `rumpun`, `provinsi_id`, `peringkat`
- Proper foreign key relationships

---
**KISS Method**: Simple, Direct, Effective
**Last Updated**: 2024-12-19
