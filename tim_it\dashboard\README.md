# Dashboard SIMAK - Dokumentasi

## 📊 Overview
Dashboard SIMAK adalah halaman utama yang menampilkan ringkasan data akreditasi sekolah di Kalimantan Timur dengan visualisasi interaktif dan real-time data.

## 🏗️ Struktur File
```
tim_it/dashboard/
├── dashboard.php           # Halaman utama dashboard
├── debug_data.php         # Tool debugging data
├── test_connection.php    # Tool testing koneksi
├── README.md             # Dokumentasi ini
└── ajax/
    ├── get_dashboard_data.php    # API data dashboard
    ├── get_todo_list.php         # API todo list
    └── update_todo_status.php    # API update todo status
```

## 🎯 Fitur Utama

### ✅ Sudah Diimplementasikan
- [x] **Statistik Sekolah per Rumpun** - Dasmen, PAUD, Kesetaraan
- [x] **Visualisasi Chart.js** - Doughnut, Bar, Line charts
- [x] **Detail Drill-down** - Klik untuk melihat detail per jenjang
- [x] **Todo List Dinamis** - Task management dengan AJAX
- [x] **<PERSON><PERSON>** - <PERSON>st<PERSON><PERSON><PERSON> sekolah per kabupaten/kota
- [x] **<PERSON><PERSON>der Kegiatan** - <PERSON><PERSON><PERSON> tanggal dan event
- [x] **Auto Refresh** - Update data otomatis setiap 5 menit
- [x] **Manual Refresh** - Tombol refresh manual
- [x] **Error Handling** - Penanganan error yang baik
- [x] **Loading States** - Indikator loading untuk UX
- [x] **Alert System** - Notifikasi sukses/error
- [x] **Responsive Design** - Mobile-friendly layout

### 📈 Data yang Ditampilkan
1. **Total Sekolah per Rumpun**
   - Dasmen (SD, SMP, SMA, SMK)
   - PAUD (TK, KB, TPA, SPS)
   - Kesetaraan (Paket A, B, C)

2. **Statistik Akreditasi**
   - Per Rumpun (A, B, C)
   - Per Jenjang (A, B, C)
   - Per Kabupaten/Kota (Top 10)

3. **Distribusi Sekolah**
   - Chart pie per jenjang
   - Sebaran geografis

4. **Todo List**
   - Task management
   - Priority levels
   - Status tracking

## 🔧 Konfigurasi

### Database Queries
Dashboard menggunakan query yang difilter berdasarkan:
- `provinsi_id` dari session user
- `status_keaktifan_id = '1'` (sekolah aktif)
- `soft_delete != '1'` (data tidak dihapus)

### Auto Refresh
- Interval: 5 menit (300000 ms)
- Data yang di-refresh: Dashboard data + Todo list
- Manual refresh: Tombol refresh di header

### Chart.js Configuration
- Version: 3.9.1
- Types: Doughnut, Bar, Line
- Responsive: true
- Maintain aspect ratio: false

## 🛠️ API Endpoints

### GET ajax/get_dashboard_data.php
**Response:**
```json
{
    "success": true,
    "data": {
        "sekolah_dasmen": [...],
        "total_dasmen": 235,
        "sekolah_paud": [...],
        "total_paud": 120,
        "sekolah_kesetaraan": [...],
        "total_kesetaraan": 45,
        "akreditasi_rumpun": [...],
        "akreditasi_jenjang": [...],
        "akreditasi_kota": [...]
    }
}
```

### GET ajax/get_todo_list.php
**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "task": "Verifikasi data akreditasi",
            "deadline": "2 hari",
            "priority": "high",
            "status": "pending"
        }
    ]
}
```

### POST ajax/update_todo_status.php
**Parameters:**
- `todo_id`: ID todo
- `status`: pending|completed

## 🎨 UI Components

### Small Boxes (Statistics)
- Color-coded per rumpun
- Click untuk drill-down
- Loading states
- Error states

### Charts
- Interactive legends
- Responsive design
- Color consistency
- Destroy/recreate untuk update

### Todo List
- Drag & drop handles
- Checkbox interactions
- Priority badges
- Status indicators

## 🔍 Debugging Tools

### debug_data.php
- Analisis struktur data
- Validasi query
- Sample data inspection
- Status checking

### test_connection.php
- Test individual queries
- Performance monitoring
- Error detection
- Connection validation

## 🚀 Performance

### Optimizations
- AJAX loading untuk data besar
- Chart destruction untuk memory management
- Efficient SQL queries dengan JOIN
- Timeout handling (30 detik)

### Caching Strategy
- Browser caching untuk assets
- Session-based data filtering
- Minimal database calls

## 🔒 Security

### Access Control
- Session-based authentication
- Level-based authorization
- CSRF protection ready
- Input validation

### Data Protection
- Prepared statements
- SQL injection prevention
- XSS protection
- Error logging

## 📱 Responsive Design

### Breakpoints
- Mobile: < 768px
- Tablet: 768px - 992px
- Desktop: > 992px

### Adaptive Features
- Collapsible sidebar
- Responsive charts
- Mobile-friendly navigation
- Touch-friendly interactions

## 🔧 Maintenance

### Regular Tasks
1. Monitor error logs
2. Check database performance
3. Update Chart.js version
4. Review todo list data
5. Validate data accuracy

### Troubleshooting
- Check debug_data.php untuk data issues
- Use test_connection.php untuk connection problems
- Monitor browser console untuk JavaScript errors
- Check PHP error logs untuk server issues

## 🔧 Query Optimization & Fixes

### Database Query Improvements
1. **Flexible Rumpun Filtering**
   - Changed from exact match to LIKE pattern matching
   - `s.rumpun = 'dasmen'` → `(s.rumpun = 'dasmen' OR s.rumpun LIKE '%dasmen%')`
   - Handles variations in rumpun naming

2. **Removed Strict Filters**
   - Removed `status_keaktifan_id = '1'` filter (too restrictive)
   - Removed `soft_delete` filter (inconsistent data)
   - Focus on provinsi_id filtering only

3. **NULL Handling**
   - Added `COALESCE(j.nm_jenjang, 'Tidak Diketahui')` for missing jenjang names
   - Proper NULL handling in all queries

4. **Fallback Data Strategy**
   - Alternative query if no rumpun-specific data found
   - Automatic categorization based on jenjang names
   - Dummy data for akreditasi if table is empty

5. **Enhanced Debugging**
   - Added comprehensive error logging
   - Debug info in API response
   - Detailed test queries in test_connection.php

### Query Examples

**Before (Restrictive):**
```sql
SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
FROM sekolah s
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
WHERE s.provinsi_id = ?
AND s.rumpun = 'dasmen'
AND s.status_keaktifan_id = '1'
AND (s.soft_delete IS NULL OR s.soft_delete != '1')
GROUP BY j.jenjang_id, j.nm_jenjang
ORDER BY j.jenjang_id
```

**After (Flexible):**
```sql
SELECT COALESCE(j.nm_jenjang, 'Tidak Diketahui') as nm_jenjang,
       COUNT(s.sekolah_id) as total
FROM sekolah s
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
WHERE s.provinsi_id = ?
AND (s.rumpun = 'dasmen' OR s.rumpun LIKE '%dasmen%')
GROUP BY j.jenjang_id, j.nm_jenjang
ORDER BY j.jenjang_id
```

### Troubleshooting Steps
1. Run `debug_data.php` to analyze data structure
2. Run `test_connection.php` to test individual queries
3. Check browser console for JavaScript errors
4. Check PHP error logs for server-side issues
5. Verify session provinsi_id is set correctly

---
**Last Updated:** 2024-12-19
**Version:** 1.1.0 (Query Optimized)
