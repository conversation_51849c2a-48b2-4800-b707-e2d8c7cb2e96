# Dashboard SIMAK - Dokumentasi

## 📊 Overview
Dashboard SIMAK adalah halaman utama yang menampilkan ringkasan data akreditasi sekolah di Kalimantan Timur dengan visualisasi interaktif dan real-time data.

## 🏗️ Struktur File
```
tim_it/dashboard/
├── dashboard.php           # Halaman utama dashboard
├── debug_data.php         # Tool debugging data
├── test_connection.php    # Tool testing koneksi
├── README.md             # Dokumentasi ini
└── ajax/
    ├── get_dashboard_data.php    # API data dashboard
    ├── get_todo_list.php         # API todo list
    └── update_todo_status.php    # API update todo status
```

## 🎯 Fitur Utama

### ✅ Sudah Diimplementasikan
- [x] **Statistik Sekolah per Rumpun** - Dasmen, PAUD, Kesetaraan
- [x] **Visualisasi Chart.js** - Doughnut, Bar, Line charts
- [x] **Detail Drill-down** - Klik untuk melihat detail per jenjang
- [x] **Todo List Dinamis** - Task management dengan AJAX
- [x] **<PERSON><PERSON>** - <PERSON>st<PERSON><PERSON><PERSON> sekolah per kabupaten/kota
- [x] **<PERSON><PERSON>der Kegiatan** - <PERSON><PERSON><PERSON> tanggal dan event
- [x] **Auto Refresh** - Update data otomatis setiap 5 menit
- [x] **Manual Refresh** - Tombol refresh manual
- [x] **Error Handling** - Penanganan error yang baik
- [x] **Loading States** - Indikator loading untuk UX
- [x] **Alert System** - Notifikasi sukses/error
- [x] **Responsive Design** - Mobile-friendly layout

### 📈 Data yang Ditampilkan
1. **Total Sekolah per Rumpun**
   - Dasmen (SD, SMP, SMA, SMK)
   - PAUD (TK, KB, TPA, SPS)
   - Kesetaraan (Paket A, B, C)

2. **Statistik Akreditasi**
   - Per Rumpun (A, B, C)
   - Per Jenjang (A, B, C)
   - Per Kabupaten/Kota (Top 10)

3. **Distribusi Sekolah**
   - Chart pie per jenjang
   - Sebaran geografis

4. **Todo List**
   - Task management
   - Priority levels
   - Status tracking

## 🔧 Konfigurasi

### Database Queries
Dashboard menggunakan query yang difilter berdasarkan:
- `provinsi_id` dari session user
- `status_keaktifan_id = '1'` (sekolah aktif)
- `soft_delete != '1'` (data tidak dihapus)

### Auto Refresh
- Interval: 5 menit (300000 ms)
- Data yang di-refresh: Dashboard data + Todo list
- Manual refresh: Tombol refresh di header

### Chart.js Configuration
- Version: 3.9.1
- Types: Doughnut, Bar, Line
- Responsive: true
- Maintain aspect ratio: false

## 🛠️ API Endpoints

### GET ajax/get_dashboard_data.php
**Response:**
```json
{
    "success": true,
    "data": {
        "sekolah_dasmen": [...],
        "total_dasmen": 235,
        "sekolah_paud": [...],
        "total_paud": 120,
        "sekolah_kesetaraan": [...],
        "total_kesetaraan": 45,
        "akreditasi_rumpun": [...],
        "akreditasi_jenjang": [...],
        "akreditasi_kota": [...]
    }
}
```

### GET ajax/get_todo_list.php
**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "task": "Verifikasi data akreditasi",
            "deadline": "2 hari",
            "priority": "high",
            "status": "pending"
        }
    ]
}
```

### POST ajax/update_todo_status.php
**Parameters:**
- `todo_id`: ID todo
- `status`: pending|completed

## 🎨 UI Components

### Small Boxes (Statistics)
- Color-coded per rumpun
- Click untuk drill-down
- Loading states
- Error states

### Charts
- Interactive legends
- Responsive design
- Color consistency
- Destroy/recreate untuk update

### Todo List
- Drag & drop handles
- Checkbox interactions
- Priority badges
- Status indicators

## 🔍 Debugging Tools

### debug_data.php
- Analisis struktur data
- Validasi query
- Sample data inspection
- Status checking

### test_connection.php
- Test individual queries
- Performance monitoring
- Error detection
- Connection validation

## 🚀 Performance

### Optimizations
- AJAX loading untuk data besar
- Chart destruction untuk memory management
- Efficient SQL queries dengan JOIN
- Timeout handling (30 detik)

### Caching Strategy
- Browser caching untuk assets
- Session-based data filtering
- Minimal database calls

## 🔒 Security

### Access Control
- Session-based authentication
- Level-based authorization
- CSRF protection ready
- Input validation

### Data Protection
- Prepared statements
- SQL injection prevention
- XSS protection
- Error logging

## 📱 Responsive Design

### Breakpoints
- Mobile: < 768px
- Tablet: 768px - 992px
- Desktop: > 992px

### Adaptive Features
- Collapsible sidebar
- Responsive charts
- Mobile-friendly navigation
- Touch-friendly interactions

## 🔧 Maintenance

### Regular Tasks
1. Monitor error logs
2. Check database performance
3. Update Chart.js version
4. Review todo list data
5. Validate data accuracy

### Troubleshooting
- Check debug_data.php untuk data issues
- Use test_connection.php untuk connection problems
- Monitor browser console untuk JavaScript errors
- Check PHP error logs untuk server issues

---
**Last Updated:** 2024-12-19
**Version:** 1.0.0
