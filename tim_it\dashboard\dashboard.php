<?php
// Include koneksi database
require_once '../../koneksi.php';

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

// Data real akan dimuat via AJAX dari get_dashboard_data.php

// Include header
include '../header.php';
?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Dashboard SIMAK</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">Dashboard</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Small boxes (Stat box) - Sekolah per Rumpun -->
            <div class="row">
                <!-- Sekolah Dasmen -->
                <div class="col-lg-4 col-md-6 col-12">
                    <div class="small-box bg-primary">
                        <div class="inner">
                            <h3 id="total-dasmen">-</h3>
                            <p>Total Sekolah Dasmen</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-school"></i>
                        </div>
                        <a href="#" class="small-box-footer" onclick="showDetailRumpun('dasmen')">
                            Detail per Jenjang <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Sekolah PAUD -->
                <div class="col-lg-4 col-md-6 col-12">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3 id="total-paud">-</h3>
                            <p>Total Sekolah PAUD</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-baby"></i>
                        </div>
                        <a href="#" class="small-box-footer" onclick="showDetailRumpun('paud')">
                            Detail per Jenjang <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Sekolah Kesetaraan -->
                <div class="col-lg-4 col-md-6 col-12">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3 id="total-kesetaraan">-</h3>
                            <p>Total Sekolah Kesetaraan</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <a href="#" class="small-box-footer" onclick="showDetailRumpun('kesetaraan')">
                            Detail per Jenjang <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Detail Cards per Jenjang (Hidden by default) -->
            <div class="row" id="detail-cards" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title" id="detail-title">Detail Sekolah per Jenjang</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" onclick="hideDetailCards()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row" id="jenjang-cards">
                                <!-- Cards akan diisi via JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.row -->

            <!-- Statistik Akreditasi -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-bar mr-1"></i>
                                Statistik Peringkat Akreditasi
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" id="akreditasiTabs" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="rumpun-tab" data-toggle="tab" href="#rumpun" role="tab">
                                        Per Rumpun
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="jenjang-tab" data-toggle="tab" href="#jenjang" role="tab">
                                        Per Jenjang
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="kota-tab" data-toggle="tab" href="#kota" role="tab">
                                        Per Kabupaten/Kota
                                    </a>
                                </li>
                            </ul>

                            <!-- Tab content -->
                            <div class="tab-content mt-3">
                                <!-- Tab Rumpun -->
                                <div class="tab-pane fade show active" id="rumpun" role="tabpanel">
                                    <canvas id="chartRumpun" height="100"></canvas>
                                </div>

                                <!-- Tab Jenjang -->
                                <div class="tab-pane fade" id="jenjang" role="tabpanel">
                                    <canvas id="chartJenjang" height="100"></canvas>
                                </div>

                                <!-- Tab Kota -->
                                <div class="tab-pane fade" id="kota" role="tabpanel">
                                    <div style="max-height: 400px; overflow-y: auto;">
                                        <canvas id="chartKota" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main row -->
            <div class="row">
                <!-- Left col -->
                <section class="col-lg-7 connectedSortable">
                    <!-- Custom tabs (Charts with tabs)-->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-pie mr-1"></i>
                                Distribusi Sekolah per Jenjang
                            </h3>
                        </div><!-- /.card-header -->
                        <div class="card-body">
                            <div class="tab-content p-0">
                                <!-- Morris chart - Sales -->
                                <div class="chart tab-pane active" id="revenue-chart" style="position: relative; height: 300px;">
                                    <canvas id="jenjangChart" height="300" style="height: 300px;"></canvas>
                                </div>
                            </div>
                        </div><!-- /.card-body -->
                    </div>
                    <!-- /.card -->

                    <!-- TO DO List -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-tasks mr-1"></i>
                                Tugas Terbaru
                            </h3>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            <ul class="todo-list" data-widget="todo-list">
                                <li>
                                    <span class="handle">
                                        <i class="fas fa-ellipsis-v"></i>
                                        <i class="fas fa-ellipsis-v"></i>
                                    </span>
                                    <div class="icheck-primary d-inline ml-2">
                                        <input type="checkbox" value="" name="todo1" id="todoCheck1">
                                        <label for="todoCheck1"></label>
                                    </div>
                                    <span class="text">Verifikasi data akreditasi SMA Negeri 1 Samarinda</span>
                                    <small class="badge badge-danger"><i class="far fa-clock"></i> 2 hari</small>
                                </li>
                                <li>
                                    <span class="handle">
                                        <i class="fas fa-ellipsis-v"></i>
                                        <i class="fas fa-ellipsis-v"></i>
                                    </span>
                                    <div class="icheck-primary d-inline ml-2">
                                        <input type="checkbox" value="" name="todo2" id="todoCheck2" checked>
                                        <label for="todoCheck2"></label>
                                    </div>
                                    <span class="text">Update data PAUD di Kutai Kartanegara</span>
                                    <small class="badge badge-success"><i class="far fa-clock"></i> Selesai</small>
                                </li>
                                <li>
                                    <span class="handle">
                                        <i class="fas fa-ellipsis-v"></i>
                                        <i class="fas fa-ellipsis-v"></i>
                                    </span>
                                    <div class="icheck-primary d-inline ml-2">
                                        <input type="checkbox" value="" name="todo3" id="todoCheck3">
                                        <label for="todoCheck3"></label>
                                    </div>
                                    <span class="text">Buat laporan bulanan akreditasi</span>
                                    <small class="badge badge-warning"><i class="far fa-clock"></i> 5 hari</small>
                                </li>
                            </ul>
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </section>
                <!-- /.Left col -->

                <!-- right col (We are only adding the ID to make the widgets sortable)-->
                <section class="col-lg-5 connectedSortable">
                    <!-- Map card -->
                    <div class="card bg-gradient-primary">
                        <div class="card-header border-0">
                            <h3 class="card-title">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                Sebaran Sekolah Kalimantan Timur
                            </h3>
                        </div>
                        <div class="card-body">
                            <div id="world-map" style="height: 250px; width: 100%;"></div>
                        </div>
                        <!-- /.card-body-->
                    </div>
                    <!-- /.card -->

                    <!-- solid sales graph -->
                    <div class="card bg-gradient-info">
                        <div class="card-header border-0">
                            <h3 class="card-title">
                                <i class="fas fa-th mr-1"></i>
                                Ringkasan Akreditasi
                            </h3>
                        </div>
                        <div class="card-body">
                            <canvas class="chart" id="line-chart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->

                    <!-- Calendar -->
                    <div class="card bg-gradient-success">
                        <div class="card-header border-0">
                            <h3 class="card-title">
                                <i class="far fa-calendar-alt"></i>
                                Kalender Kegiatan
                            </h3>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body pt-0">
                            <!-- The calendar -->
                            <div id="calendar" style="width: 100%"></div>
                        </div>
                        <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                </section>
                <!-- right col -->
            </div>
            <!-- /.row (main row) -->
        </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->

<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- Page specific script -->
<script>
// Global variables untuk menyimpan data dan charts
let dashboardData = {};
let chartRumpun, chartJenjang, chartKota, jenjangChart, lineChart;

$(document).ready(function() {
    loadDashboardData();
});

// Load data dashboard dari server
function loadDashboardData() {
    $.ajax({
        url: 'ajax/get_dashboard_data.php',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                dashboardData = response.data;
                updateDashboard();
            } else {
                console.error('Error loading dashboard data:', response.message);
                showAlert('error', 'Gagal memuat data dashboard');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            showAlert('error', 'Terjadi kesalahan saat memuat data dashboard');
        }
    });
}

// Update dashboard dengan data real
function updateDashboard() {
    // Update total sekolah per rumpun
    $('#total-dasmen').text(dashboardData.total_dasmen.toLocaleString());
    $('#total-paud').text(dashboardData.total_paud.toLocaleString());
    $('#total-kesetaraan').text(dashboardData.total_kesetaraan.toLocaleString());

    // Create charts
    createJenjangChart();
    createAkreditasiCharts();
    createLineChart();
}

// Chart distribusi sekolah per jenjang (gabungan semua rumpun)
function createJenjangChart() {
    const ctx = document.getElementById('jenjangChart').getContext('2d');

    // Gabungkan data dari semua rumpun
    const jenjangData = {};

    // Proses data dasmen
    dashboardData.sekolah_dasmen.forEach(item => {
        if (jenjangData[item.nm_jenjang]) {
            jenjangData[item.nm_jenjang] += parseInt(item.total);
        } else {
            jenjangData[item.nm_jenjang] = parseInt(item.total);
        }
    });

    // Proses data paud
    dashboardData.sekolah_paud.forEach(item => {
        if (jenjangData[item.nm_jenjang]) {
            jenjangData[item.nm_jenjang] += parseInt(item.total);
        } else {
            jenjangData[item.nm_jenjang] = parseInt(item.total);
        }
    });

    // Proses data kesetaraan
    dashboardData.sekolah_kesetaraan.forEach(item => {
        if (jenjangData[item.nm_jenjang]) {
            jenjangData[item.nm_jenjang] += parseInt(item.total);
        } else {
            jenjangData[item.nm_jenjang] = parseInt(item.total);
        }
    });

    const labels = Object.keys(jenjangData);
    const data = Object.values(jenjangData);
    const colors = ['#f56954', '#00a65a', '#f39c12', '#00c0ef', '#3c8dbc', '#d2d6de', '#39cccc'];

    if (jenjangChart) {
        jenjangChart.destroy();
    }

    jenjangChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
            }]
        },
        options: {
            maintainAspectRatio: false,
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Charts untuk statistik akreditasi
function createAkreditasiCharts() {
    createChartRumpun();
    createChartJenjang();
    createChartKota();
}

// Chart akreditasi per rumpun
function createChartRumpun() {
    const ctx = document.getElementById('chartRumpun').getContext('2d');

    // Proses data akreditasi per rumpun
    const rumpunData = {};
    dashboardData.akreditasi_rumpun.forEach(item => {
        if (!rumpunData[item.rumpun]) {
            rumpunData[item.rumpun] = { A: 0, B: 0, C: 0 };
        }
        rumpunData[item.rumpun][item.peringkat] = parseInt(item.total);
    });

    const labels = Object.keys(rumpunData);
    const dataA = labels.map(rumpun => rumpunData[rumpun].A || 0);
    const dataB = labels.map(rumpun => rumpunData[rumpun].B || 0);
    const dataC = labels.map(rumpun => rumpunData[rumpun].C || 0);

    if (chartRumpun) {
        chartRumpun.destroy();
    }

    chartRumpun = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Akreditasi A',
                    data: dataA,
                    backgroundColor: '#28a745'
                },
                {
                    label: 'Akreditasi B',
                    data: dataB,
                    backgroundColor: '#ffc107'
                },
                {
                    label: 'Akreditasi C',
                    data: dataC,
                    backgroundColor: '#dc3545'
                }
            ]
        },
        options: {
            responsive: true,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });
}

// Chart akreditasi per jenjang
function createChartJenjang() {
    const ctx = document.getElementById('chartJenjang').getContext('2d');

    // Proses data akreditasi per jenjang
    const jenjangData = {};
    dashboardData.akreditasi_jenjang.forEach(item => {
        if (!jenjangData[item.nm_jenjang]) {
            jenjangData[item.nm_jenjang] = { A: 0, B: 0, C: 0 };
        }
        jenjangData[item.nm_jenjang][item.peringkat] = parseInt(item.total);
    });

    const labels = Object.keys(jenjangData);
    const dataA = labels.map(jenjang => jenjangData[jenjang].A || 0);
    const dataB = labels.map(jenjang => jenjangData[jenjang].B || 0);
    const dataC = labels.map(jenjang => jenjangData[jenjang].C || 0);

    if (chartJenjang) {
        chartJenjang.destroy();
    }

    chartJenjang = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Akreditasi A',
                    data: dataA,
                    backgroundColor: '#28a745'
                },
                {
                    label: 'Akreditasi B',
                    data: dataB,
                    backgroundColor: '#ffc107'
                },
                {
                    label: 'Akreditasi C',
                    data: dataC,
                    backgroundColor: '#dc3545'
                }
            ]
        },
        options: {
            responsive: true,
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });
}

// Chart akreditasi per kabupaten/kota
function createChartKota() {
    const ctx = document.getElementById('chartKota').getContext('2d');

    // Proses data akreditasi per kota
    const kotaData = {};
    dashboardData.akreditasi_kota.forEach(item => {
        if (!kotaData[item.nm_kota]) {
            kotaData[item.nm_kota] = { A: 0, B: 0, C: 0 };
        }
        kotaData[item.nm_kota][item.peringkat] = parseInt(item.total);
    });

    // Ambil top 10 kota berdasarkan total akreditasi
    const kotaList = Object.keys(kotaData).map(kota => ({
        nama: kota,
        total: (kotaData[kota].A || 0) + (kotaData[kota].B || 0) + (kotaData[kota].C || 0)
    })).sort((a, b) => b.total - a.total).slice(0, 10);

    const labels = kotaList.map(item => item.nama);
    const dataA = labels.map(kota => kotaData[kota].A || 0);
    const dataB = labels.map(kota => kotaData[kota].B || 0);
    const dataC = labels.map(kota => kotaData[kota].C || 0);

    if (chartKota) {
        chartKota.destroy();
    }

    chartKota = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Akreditasi A',
                    data: dataA,
                    backgroundColor: '#28a745'
                },
                {
                    label: 'Akreditasi B',
                    data: dataB,
                    backgroundColor: '#ffc107'
                },
                {
                    label: 'Akreditasi C',
                    data: dataC,
                    backgroundColor: '#dc3545'
                }
            ]
        },
        options: {
            responsive: true,
            indexAxis: 'y',
            scales: {
                xAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }],
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });
}

// Line Chart untuk trend (placeholder - bisa dikembangkan lebih lanjut)
function createLineChart() {
    const lineCtx = document.getElementById('line-chart');
    if (!lineCtx) return;

    const ctx = lineCtx.getContext('2d');

    if (lineChart) {
        lineChart.destroy();
    }

    lineChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun'],
            datasets: [{
                label: 'Akreditasi Baru',
                borderColor: 'rgba(255,255,255,.8)',
                backgroundColor: 'rgba(255,255,255,.4)',
                pointBorderColor: 'rgba(255,255,255,.8)',
                pointBackgroundColor: 'rgba(255,255,255,.8)',
                data: [15, 23, 18, 32, 28, 35]
            }]
        },
        options: {
            maintainAspectRatio: false,
            responsive: true,
            legend: {
                display: false
            },
            scales: {
                yAxes: [{
                    gridLines: {
                        display: false,
                        color: 'rgba(255,255,255,.2)',
                        zeroLineColor: 'transparent'
                    },
                    ticks: {
                        fontColor: 'rgba(255,255,255,.8)'
                    }
                }],
                xAxes: [{
                    gridLines: {
                        display: false,
                        color: 'rgba(255,255,255,.2)',
                        zeroLineColor: 'transparent'
                    },
                    ticks: {
                        fontColor: 'rgba(255,255,255,.8)'
                    }
                }]
            }
        }
    });
}

// Fungsi untuk menampilkan detail rumpun
function showDetailRumpun(rumpun) {
    let data, title, bgColors;

    switch(rumpun) {
        case 'dasmen':
            data = dashboardData.sekolah_dasmen;
            title = 'Detail Sekolah Dasmen per Jenjang';
            bgColors = ['#007bff', '#0056b3', '#004085'];
            break;
        case 'paud':
            data = dashboardData.sekolah_paud;
            title = 'Detail Sekolah PAUD per Jenjang';
            bgColors = ['#28a745', '#1e7e34', '#155724'];
            break;
        case 'kesetaraan':
            data = dashboardData.sekolah_kesetaraan;
            title = 'Detail Sekolah Kesetaraan per Jenjang';
            bgColors = ['#ffc107', '#e0a800', '#d39e00'];
            break;
    }

    $('#detail-title').text(title);

    let cardsHtml = '';
    data.forEach((item, index) => {
        const bgColor = bgColors[index % bgColors.length];
        cardsHtml += `
            <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-3">
                <div class="small-box" style="background-color: ${bgColor}; color: white;">
                    <div class="inner">
                        <h3>${parseInt(item.total).toLocaleString()}</h3>
                        <p>${item.nm_jenjang || 'Tidak Diketahui'}</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                </div>
            </div>
        `;
    });

    $('#jenjang-cards').html(cardsHtml);
    $('#detail-cards').slideDown();
}

// Fungsi untuk menyembunyikan detail cards
function hideDetailCards() {
    $('#detail-cards').slideUp();
}

// Fungsi alert helper
function showAlert(type, message) {
    // Implementasi alert sesuai dengan sistem yang ada
    console.log(`${type.toUpperCase()}: ${message}`);
}
