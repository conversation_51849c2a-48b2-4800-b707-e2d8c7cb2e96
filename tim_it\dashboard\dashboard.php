<?php
// Include koneksi database
require_once '../../koneksi.php';

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

// Data real akan dimuat via AJAX dari get_dashboard_data.php

// Include header
include '../header.php';
?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Dashboard SIMAK</h1>
                </div>
                <div class="col-sm-6">
                    <div class="float-sm-right">
                        <button type="button" class="btn btn-sm btn-outline-primary mr-2" id="btn-refresh" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <ol class="breadcrumb d-inline-block mb-0">
                            <li class="breadcrumb-item"><a href="#">Home</a></li>
                            <li class="breadcrumb-item active">Dashboard</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">

            <!-- Alert Messages -->
            <div id="alert-container"></div>
            <!-- Small boxes (Stat box) - Sekolah per Rumpun -->
            <div class="row">
                <!-- Sekolah Dasmen -->
                <div class="col-lg-4 col-md-6 col-12">
                    <div class="small-box bg-primary">
                        <div class="inner">
                            <h3 id="total-dasmen">-</h3>
                            <p>Total Sekolah Dasmen</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-school"></i>
                        </div>
                        <a href="#" class="small-box-footer" onclick="showDetailRumpun('dasmen')">
                            Detail per Jenjang <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Sekolah PAUD -->
                <div class="col-lg-4 col-md-6 col-12">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3 id="total-paud">-</h3>
                            <p>Total Sekolah PAUD</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-baby"></i>
                        </div>
                        <a href="#" class="small-box-footer" onclick="showDetailRumpun('paud')">
                            Detail per Jenjang <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Sekolah Kesetaraan -->
                <div class="col-lg-4 col-md-6 col-12">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3 id="total-kesetaraan">-</h3>
                            <p>Total Sekolah Kesetaraan</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <a href="#" class="small-box-footer" onclick="showDetailRumpun('kesetaraan')">
                            Detail per Jenjang <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Detail Cards per Jenjang (Hidden by default) -->
            <div class="row" id="detail-cards" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title" id="detail-title">Detail Sekolah per Jenjang</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" onclick="hideDetailCards()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row" id="jenjang-cards">
                                <!-- Cards akan diisi via JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /.row -->

            <!-- Statistik Akreditasi -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-bar mr-1"></i>
                                Statistik Peringkat Akreditasi
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" id="akreditasiTabs" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="rumpun-tab" data-toggle="tab" href="#rumpun" role="tab">
                                        Per Rumpun
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="jenjang-tab" data-toggle="tab" href="#jenjang" role="tab">
                                        Per Jenjang
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="kota-tab" data-toggle="tab" href="#kota" role="tab">
                                        Per Kabupaten/Kota
                                    </a>
                                </li>
                            </ul>

                            <!-- Tab content -->
                            <div class="tab-content mt-3">
                                <!-- Tab Rumpun -->
                                <div class="tab-pane fade show active" id="rumpun" role="tabpanel">
                                    <canvas id="chartRumpun" height="100"></canvas>
                                </div>

                                <!-- Tab Jenjang -->
                                <div class="tab-pane fade" id="jenjang" role="tabpanel">
                                    <canvas id="chartJenjang" height="100"></canvas>
                                </div>

                                <!-- Tab Kota -->
                                <div class="tab-pane fade" id="kota" role="tabpanel">
                                    <div style="max-height: 400px; overflow-y: auto;">
                                        <canvas id="chartKota" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main row -->
            <div class="row">
                <!-- Left col -->
                <section class="col-lg-7 connectedSortable">
                    <!-- Custom tabs (Charts with tabs)-->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-pie mr-1"></i>
                                Distribusi Sekolah per Jenjang
                            </h3>
                        </div><!-- /.card-header -->
                        <div class="card-body">
                            <div class="tab-content p-0">
                                <!-- Morris chart - Sales -->
                                <div class="chart tab-pane active" id="revenue-chart" style="position: relative; height: 300px;">
                                    <canvas id="jenjangChart" height="300" style="height: 300px;"></canvas>
                                </div>
                            </div>
                        </div><!-- /.card-body -->
                    </div>
                    <!-- /.card -->


                </section>
                <!-- /.Left col -->

                <!-- right col (We are only adding the ID to make the widgets sortable)-->
                <section class="col-lg-5 connectedSortable">
                    <!-- Map card -->
                    <div class="card bg-gradient-primary">
                        <div class="card-header border-0">
                            <h3 class="card-title">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                Sebaran Sekolah Kalimantan Timur
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Tab Navigation -->
                            <ul class="nav nav-pills nav-fill mb-3" id="sebaranTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active text-white bg-transparent border border-white" id="dasmen-tab" data-toggle="pill" href="#dasmen-content" role="tab">
                                        <i class="fas fa-school mr-1"></i> Dasmen
                                    </a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link text-white bg-transparent border border-white" id="paud-tab" data-toggle="pill" href="#paud-content" role="tab">
                                        <i class="fas fa-baby mr-1"></i> PAUD
                                    </a>
                                </li>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content" id="sebaranTabContent">
                                <!-- Dasmen Content -->
                                <div class="tab-pane fade show active" id="dasmen-content" role="tabpanel">
                                    <div id="sebaran-dasmen" style="height: 250px; width: 100%;">
                                        <div class="d-flex align-items-center justify-content-center h-100">
                                            <div class="text-center text-white">
                                                <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                                                <p class="mb-0">Memuat data Dasmen...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- PAUD Content -->
                                <div class="tab-pane fade" id="paud-content" role="tabpanel">
                                    <div id="sebaran-paud" style="height: 250px; width: 100%;">
                                        <div class="d-flex align-items-center justify-content-center h-100">
                                            <div class="text-center text-white">
                                                <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                                                <p class="mb-0">Memuat data PAUD...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- /.card-body-->
                    </div>
                    <!-- /.card -->




                </section>
                <!-- right col -->
            </div>
            <!-- /.row (main row) -->
        </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->

<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- Page specific script -->
<script>
// Global variables untuk menyimpan data dan charts
let dashboardData = {};
let chartRumpun, chartJenjang, chartKota, jenjangChart, lineChart;

$(document).ready(function() {
    loadDashboardData();
    initializeMap();

    // Auto refresh setiap 5 menit
    setInterval(function() {
        loadDashboardData();
    }, 300000); // 5 menit = 300000 ms
});

// Fungsi refresh manual
function refreshDashboard() {
    const $btn = $('#btn-refresh');
    const originalHtml = $btn.html();

    // Show loading state
    $btn.html('<i class="fas fa-spinner fa-spin"></i> Refreshing...').prop('disabled', true);

    // Reload semua data
    loadDashboardData();

    // Reset button setelah 2 detik
    setTimeout(function() {
        $btn.html(originalHtml).prop('disabled', false);
    }, 2000);
}

// Load data dashboard dari server
function loadDashboardData() {
    // Show loading state
    $('#total-dasmen').html('<i class="fas fa-spinner fa-spin"></i>');
    $('#total-paud').html('<i class="fas fa-spinner fa-spin"></i>');
    $('#total-kesetaraan').html('<i class="fas fa-spinner fa-spin"></i>');

    $.ajax({
        url: 'ajax/get_dashboard_data.php',
        type: 'GET',
        dataType: 'json',
        timeout: 30000, // 30 detik timeout
        success: function(response) {
            console.log('Dashboard API Response:', response); // Debug log

            if (response.success) {
                dashboardData = response.data;
                updateDashboard();
                // showAlert('success', 'Data dashboard berhasil dimuat'); // Disabled untuk KISS

                console.log('Dashboard data loaded successfully');
            } else {
                console.error('Error loading dashboard data:', response.message);
                showAlert('error', 'Gagal memuat data dashboard: ' + (response.message || 'Unknown error'));
                setErrorState();
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            let errorMessage = 'Terjadi kesalahan saat memuat data dashboard';

            if (status === 'timeout') {
                errorMessage = 'Timeout: Server terlalu lama merespons';
            } else if (xhr.status === 403) {
                errorMessage = 'Akses ditolak. Silakan login ulang';
            } else if (xhr.status === 500) {
                errorMessage = 'Kesalahan server internal';
            }

            showAlert('error', errorMessage);
            setErrorState();
        }
    });
}

// Set error state untuk dashboard
function setErrorState() {
    $('#total-dasmen').text('Error');
    $('#total-paud').text('Error');
    $('#total-kesetaraan').text('Error');
}

// Update dashboard dengan data real
function updateDashboard() {
    console.log('Updating dashboard with data:', dashboardData); // Debug log

    // Validasi data
    if (!dashboardData) {
        console.error('Dashboard data is null or undefined');
        setErrorState();
        return;
    }

    // Update total sekolah per rumpun dengan fallback
    $('#total-dasmen').text((dashboardData.total_dasmen || 0).toLocaleString());
    $('#total-paud').text((dashboardData.total_paud || 0).toLocaleString());
    $('#total-kesetaraan').text((dashboardData.total_kesetaraan || 0).toLocaleString());

    // Create charts
    createJenjangChart();
    createAkreditasiCharts();
    createLineChart();

    // Update sebaran data
    updateSebaranDasmen();
    updateSebaranPaud();
}

// Chart distribusi sekolah per jenjang (gabungan semua rumpun)
function createJenjangChart() {
    const ctx = document.getElementById('jenjangChart').getContext('2d');

    // Gabungkan data dari semua rumpun
    const jenjangData = {};

    // Proses data dasmen dengan validasi
    if (dashboardData.sekolah_dasmen && Array.isArray(dashboardData.sekolah_dasmen)) {
        dashboardData.sekolah_dasmen.forEach(item => {
            if (item && item.nm_jenjang && item.total) {
                if (jenjangData[item.nm_jenjang]) {
                    jenjangData[item.nm_jenjang] += parseInt(item.total);
                } else {
                    jenjangData[item.nm_jenjang] = parseInt(item.total);
                }
            }
        });
    }

    // Proses data paud dengan validasi
    if (dashboardData.sekolah_paud && Array.isArray(dashboardData.sekolah_paud)) {
        dashboardData.sekolah_paud.forEach(item => {
            if (item && item.nm_jenjang && item.total) {
                if (jenjangData[item.nm_jenjang]) {
                    jenjangData[item.nm_jenjang] += parseInt(item.total);
                } else {
                    jenjangData[item.nm_jenjang] = parseInt(item.total);
                }
            }
        });
    }

    // Proses data kesetaraan dengan validasi
    if (dashboardData.sekolah_kesetaraan && Array.isArray(dashboardData.sekolah_kesetaraan)) {
        dashboardData.sekolah_kesetaraan.forEach(item => {
            if (item && item.nm_jenjang && item.total) {
                if (jenjangData[item.nm_jenjang]) {
                    jenjangData[item.nm_jenjang] += parseInt(item.total);
                } else {
                    jenjangData[item.nm_jenjang] = parseInt(item.total);
                }
            }
        });
    }

    const labels = Object.keys(jenjangData);
    const data = Object.values(jenjangData);
    const colors = ['#f56954', '#00a65a', '#f39c12', '#00c0ef', '#3c8dbc', '#d2d6de', '#39cccc'];

    if (jenjangChart) {
        jenjangChart.destroy();
    }

    jenjangChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
            }]
        },
        options: {
            maintainAspectRatio: false,
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Charts untuk statistik akreditasi
function createAkreditasiCharts() {
    createChartRumpun();
    createChartJenjang();
    createChartKota();
}

// Chart akreditasi per rumpun
function createChartRumpun() {
    const ctx = document.getElementById('chartRumpun').getContext('2d');

    // Proses data akreditasi per rumpun
    const rumpunData = {};
    dashboardData.akreditasi_rumpun.forEach(item => {
        if (!rumpunData[item.rumpun]) {
            rumpunData[item.rumpun] = { A: 0, B: 0, C: 0 };
        }
        rumpunData[item.rumpun][item.peringkat] = parseInt(item.total);
    });

    const labels = Object.keys(rumpunData);
    const dataA = labels.map(rumpun => rumpunData[rumpun].A || 0);
    const dataB = labels.map(rumpun => rumpunData[rumpun].B || 0);
    const dataC = labels.map(rumpun => rumpunData[rumpun].C || 0);

    if (chartRumpun) {
        chartRumpun.destroy();
    }

    chartRumpun = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Akreditasi A',
                    data: dataA,
                    backgroundColor: '#28a745'
                },
                {
                    label: 'Akreditasi B',
                    data: dataB,
                    backgroundColor: '#ffc107'
                },
                {
                    label: 'Akreditasi C',
                    data: dataC,
                    backgroundColor: '#dc3545'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// Chart akreditasi per jenjang
function createChartJenjang() {
    const ctx = document.getElementById('chartJenjang').getContext('2d');

    // Proses data akreditasi per jenjang
    const jenjangData = {};
    dashboardData.akreditasi_jenjang.forEach(item => {
        if (!jenjangData[item.nm_jenjang]) {
            jenjangData[item.nm_jenjang] = { A: 0, B: 0, C: 0 };
        }
        jenjangData[item.nm_jenjang][item.peringkat] = parseInt(item.total);
    });

    const labels = Object.keys(jenjangData);
    const dataA = labels.map(jenjang => jenjangData[jenjang].A || 0);
    const dataB = labels.map(jenjang => jenjangData[jenjang].B || 0);
    const dataC = labels.map(jenjang => jenjangData[jenjang].C || 0);

    if (chartJenjang) {
        chartJenjang.destroy();
    }

    chartJenjang = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Akreditasi A',
                    data: dataA,
                    backgroundColor: '#28a745'
                },
                {
                    label: 'Akreditasi B',
                    data: dataB,
                    backgroundColor: '#ffc107'
                },
                {
                    label: 'Akreditasi C',
                    data: dataC,
                    backgroundColor: '#dc3545'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// Chart akreditasi per kabupaten/kota
function createChartKota() {
    const ctx = document.getElementById('chartKota').getContext('2d');

    // Proses data akreditasi per kota
    const kotaData = {};
    dashboardData.akreditasi_kota.forEach(item => {
        if (!kotaData[item.nm_kota]) {
            kotaData[item.nm_kota] = { A: 0, B: 0, C: 0 };
        }
        kotaData[item.nm_kota][item.peringkat] = parseInt(item.total);
    });

    // Ambil top 10 kota berdasarkan total akreditasi
    const kotaList = Object.keys(kotaData).map(kota => ({
        nama: kota,
        total: (kotaData[kota].A || 0) + (kotaData[kota].B || 0) + (kotaData[kota].C || 0)
    })).sort((a, b) => b.total - a.total).slice(0, 10);

    const labels = kotaList.map(item => item.nama);
    const dataA = labels.map(kota => kotaData[kota].A || 0);
    const dataB = labels.map(kota => kotaData[kota].B || 0);
    const dataC = labels.map(kota => kotaData[kota].C || 0);

    if (chartKota) {
        chartKota.destroy();
    }

    chartKota = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Akreditasi A',
                    data: dataA,
                    backgroundColor: '#28a745'
                },
                {
                    label: 'Akreditasi B',
                    data: dataB,
                    backgroundColor: '#ffc107'
                },
                {
                    label: 'Akreditasi C',
                    data: dataC,
                    backgroundColor: '#dc3545'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true
                },
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// Line Chart untuk trend (placeholder - bisa dikembangkan lebih lanjut)
function createLineChart() {
    const lineCtx = document.getElementById('line-chart');
    if (!lineCtx) return;

    const ctx = lineCtx.getContext('2d');

    if (lineChart) {
        lineChart.destroy();
    }

    lineChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun'],
            datasets: [{
                label: 'Akreditasi Baru',
                borderColor: 'rgba(255,255,255,.8)',
                backgroundColor: 'rgba(255,255,255,.4)',
                pointBorderColor: 'rgba(255,255,255,.8)',
                pointBackgroundColor: 'rgba(255,255,255,.8)',
                data: [15, 23, 18, 32, 28, 35]
            }]
        },
        options: {
            maintainAspectRatio: false,
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    grid: {
                        display: false,
                        color: 'rgba(255,255,255,.2)'
                    },
                    ticks: {
                        color: 'rgba(255,255,255,.8)'
                    }
                },
                x: {
                    grid: {
                        display: false,
                        color: 'rgba(255,255,255,.2)'
                    },
                    ticks: {
                        color: 'rgba(255,255,255,.8)'
                    }
                }
            }
        }
    });
}

// Fungsi untuk menampilkan detail rumpun
function showDetailRumpun(rumpun) {
    let data, title, bgColors;

    switch(rumpun) {
        case 'dasmen':
            data = dashboardData.sekolah_dasmen;
            title = 'Detail Sekolah Dasmen per Jenjang';
            bgColors = ['#007bff', '#0056b3', '#004085'];
            break;
        case 'paud':
            data = dashboardData.sekolah_paud;
            title = 'Detail Sekolah PAUD per Jenjang';
            bgColors = ['#28a745', '#1e7e34', '#155724'];
            break;
        case 'kesetaraan':
            data = dashboardData.sekolah_kesetaraan;
            title = 'Detail Sekolah Kesetaraan per Jenjang';
            bgColors = ['#ffc107', '#e0a800', '#d39e00'];
            break;
    }

    $('#detail-title').text(title);

    let cardsHtml = '';
    data.forEach((item, index) => {
        const bgColor = bgColors[index % bgColors.length];
        cardsHtml += `
            <div class="col-lg-3 col-md-4 col-sm-6 col-12 mb-3">
                <div class="small-box" style="background-color: ${bgColor}; color: white;">
                    <div class="inner">
                        <h3>${parseInt(item.total).toLocaleString()}</h3>
                        <p>${item.nm_jenjang || 'Tidak Diketahui'}</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                </div>
            </div>
        `;
    });

    $('#jenjang-cards').html(cardsHtml);
    $('#detail-cards').slideDown();
}

// Fungsi untuk menyembunyikan detail cards
function hideDetailCards() {
    $('#detail-cards').slideUp();
}

// Fungsi alert helper
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    // Tambahkan alert ke container
    $('#alert-container').html(alertHtml);

    // Auto hide setelah 5 detik
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}



// Initialize Map dengan data per rumpun
function initializeMap() {
    updateSebaranDasmen();
    updateSebaranPaud();
}

// Update sebaran Dasmen
function updateSebaranDasmen() {
    if (!dashboardData || !dashboardData.sekolah_dasmen) {
        $('#sebaran-dasmen').html(`
            <div class="d-flex align-items-center justify-content-center h-100">
                <div class="text-center text-white">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p class="mb-0">Data Dasmen tidak tersedia</p>
                </div>
            </div>
        `);
        return;
    }

    let html = '<div class="row text-white">';
    html += '<div class="col-12 mb-3"><h6 class="text-center text-white"><i class="fas fa-school mr-2"></i>Sebaran Sekolah Dasmen</h6></div>';

    // Hitung total per jenjang
    const jenjangCount = {};
    dashboardData.sekolah_dasmen.forEach(item => {
        if (item.nm_jenjang && item.total) {
            jenjangCount[item.nm_jenjang] = parseInt(item.total);
        }
    });

    // Tampilkan data per jenjang
    Object.entries(jenjangCount).forEach(([jenjang, total]) => {
        const percentage = dashboardData.total_dasmen > 0 ? ((total / dashboardData.total_dasmen) * 100).toFixed(1) : 0;
        html += `
            <div class="col-12 mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-graduation-cap mr-1"></i> ${jenjang}</span>
                    <div>
                        <span class="badge badge-light text-dark mr-1">${total} sekolah</span>
                        <small class="text-white-50">(${percentage}%)</small>
                    </div>
                </div>
                <div class="progress mt-1" style="height: 4px;">
                    <div class="progress-bar bg-light" style="width: ${percentage}%"></div>
                </div>
            </div>
        `;
    });

    html += `
        <div class="col-12 mt-3 pt-2 border-top border-white-50">
            <div class="d-flex justify-content-between align-items-center">
                <strong><i class="fas fa-school mr-1"></i> Total Dasmen</strong>
                <strong class="badge badge-light text-dark">${dashboardData.total_dasmen} sekolah</strong>
            </div>
        </div>
    `;
    html += '</div>';

    $('#sebaran-dasmen').html(html);
}

// Update sebaran PAUD
function updateSebaranPaud() {
    if (!dashboardData || !dashboardData.sekolah_paud) {
        $('#sebaran-paud').html(`
            <div class="d-flex align-items-center justify-content-center h-100">
                <div class="text-center text-white">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p class="mb-0">Data PAUD tidak tersedia</p>
                </div>
            </div>
        `);
        return;
    }

    let html = '<div class="row text-white">';
    html += '<div class="col-12 mb-3"><h6 class="text-center text-white"><i class="fas fa-baby mr-2"></i>Sebaran Sekolah PAUD</h6></div>';

    // Hitung total per jenjang
    const jenjangCount = {};
    dashboardData.sekolah_paud.forEach(item => {
        if (item.nm_jenjang && item.total) {
            jenjangCount[item.nm_jenjang] = parseInt(item.total);
        }
    });

    // Tampilkan data per jenjang
    Object.entries(jenjangCount).forEach(([jenjang, total]) => {
        const percentage = dashboardData.total_paud > 0 ? ((total / dashboardData.total_paud) * 100).toFixed(1) : 0;
        html += `
            <div class="col-12 mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-child mr-1"></i> ${jenjang}</span>
                    <div>
                        <span class="badge badge-light text-dark mr-1">${total} sekolah</span>
                        <small class="text-white-50">(${percentage}%)</small>
                    </div>
                </div>
                <div class="progress mt-1" style="height: 4px;">
                    <div class="progress-bar bg-light" style="width: ${percentage}%"></div>
                </div>
            </div>
        `;
    });

    html += `
        <div class="col-12 mt-3 pt-2 border-top border-white-50">
            <div class="d-flex justify-content-between align-items-center">
                <strong><i class="fas fa-baby mr-1"></i> Total PAUD</strong>
                <strong class="badge badge-light text-dark">${dashboardData.total_paud} sekolah</strong>
            </div>
        </div>
    `;
    html += '</div>';

    $('#sebaran-paud').html(html);
}
</script>
