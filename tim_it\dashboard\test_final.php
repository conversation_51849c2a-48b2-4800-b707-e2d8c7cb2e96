<?php
/**
 * Test final untuk memastikan dashboard berfungsi 100%
 */

// Include koneksi database
require_once '../../koneksi.php';

// Include session checker
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

$provinsi_id = $_SESSION['provinsi_id'] ?? 64;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Final Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9; }
        .result { background-color: #fff; padding: 10px; margin: 10px 0; border-radius: 3px; border-left: 4px solid #007bff; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 3px; }
        button:hover { background: #0056b3; }
        .status-ok { border-left-color: #28a745; }
        .status-error { border-left-color: #dc3545; }
        .status-warning { border-left-color: #ffc107; }
    </style>
</head>
<body>
    <h1>🔍 Test Final Dashboard SIMAK</h1>
    <p><strong>Provinsi ID:</strong> <?php echo $provinsi_id; ?></p>
    
    <div class="box">
        <h2>✅ 1. Database & Query Test</h2>
        <?php
        // Test query dasmen
        $query_dasmen = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
                         FROM sekolah s
                         LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                         WHERE s.provinsi_id = ? AND s.rumpun = 'dasmen'
                         GROUP BY j.jenjang_id, j.nm_jenjang";
        
        $stmt = $conn->prepare($query_dasmen);
        $stmt->bind_param("i", $provinsi_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $total_dasmen = 0;
        while ($row = $result->fetch_assoc()) {
            $total_dasmen += $row['total'];
        }
        
        // Test query paud
        $query_paud = "SELECT COUNT(s.sekolah_id) as total
                       FROM sekolah s
                       WHERE s.provinsi_id = ? AND s.rumpun = 'paud'";
        $stmt = $conn->prepare($query_paud);
        $stmt->bind_param("i", $provinsi_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $total_paud = $row['total'];
        
        // Test query kesetaraan
        $query_kesetaraan = "SELECT COUNT(s.sekolah_id) as total
                             FROM sekolah s
                             WHERE s.provinsi_id = ? AND s.rumpun = 'kesetaraan'";
        $stmt = $conn->prepare($query_kesetaraan);
        $stmt->bind_param("i", $provinsi_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $total_kesetaraan = $row['total'];
        
        $total_all = $total_dasmen + $total_paud + $total_kesetaraan;
        
        if ($total_all > 0) {
            echo "<div class='result status-ok'>";
            echo "<p class='success'>✓ Database Query: BERHASIL</p>";
            echo "<p>Total Dasmen: $total_dasmen</p>";
            echo "<p>Total PAUD: $total_paud</p>";
            echo "<p>Total Kesetaraan: $total_kesetaraan</p>";
            echo "<p><strong>Total Semua: $total_all</strong></p>";
            echo "</div>";
        } else {
            echo "<div class='result status-error'>";
            echo "<p class='error'>✗ Database Query: TIDAK ADA DATA</p>";
            echo "<p>Tidak ada data sekolah di provinsi ID: $provinsi_id</p>";
            echo "</div>";
        }
        ?>
    </div>
    
    <div class="box">
        <h2>🌐 2. API Endpoint Test</h2>
        <button onclick="testAPI()">Test API get_dashboard_data.php</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="box">
        <h2>📊 3. Chart Libraries Test</h2>
        <button onclick="testLibraries()">Test Chart.js & jQuery</button>
        <div id="library-result" class="result"></div>
    </div>
    
    <div class="box">
        <h2>🎯 4. Dashboard Integration Test</h2>
        <button onclick="testDashboardIntegration()">Test Full Integration</button>
        <div id="integration-result" class="result"></div>
    </div>
    
    <div class="box">
        <h2>🚀 5. Final Status</h2>
        <div id="final-status" class="result">
            <p class="info">Jalankan semua test di atas untuk melihat status final</p>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    
    <script>
    let testResults = {
        database: <?php echo $total_all > 0 ? 'true' : 'false'; ?>,
        api: false,
        libraries: false,
        integration: false
    };
    
    function updateFinalStatus() {
        const allPassed = Object.values(testResults).every(result => result === true);
        const finalDiv = document.getElementById('final-status');
        
        if (allPassed) {
            finalDiv.className = 'result status-ok';
            finalDiv.innerHTML = `
                <p class="success">🎉 SEMUA TEST BERHASIL!</p>
                <p>Dashboard siap digunakan:</p>
                <p><a href="dashboard.php" target="_blank" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;">🚀 BUKA DASHBOARD</a></p>
            `;
        } else {
            const failedTests = Object.entries(testResults)
                .filter(([key, value]) => value === false)
                .map(([key, value]) => key);
            
            finalDiv.className = 'result status-warning';
            finalDiv.innerHTML = `
                <p class="warning">⚠️ ADA TEST YANG GAGAL</p>
                <p>Test yang gagal: ${failedTests.join(', ')}</p>
                <p>Perbaiki masalah di atas sebelum menggunakan dashboard.</p>
            `;
        }
    }
    
    function testAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.innerHTML = '<p class="info">Testing API...</p>';
        
        fetch('ajax/get_dashboard_data.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    testResults.api = true;
                    resultDiv.className = 'result status-ok';
                    resultDiv.innerHTML = `
                        <p class="success">✓ API Test: BERHASIL</p>
                        <p>Total Dasmen: ${data.data.total_dasmen}</p>
                        <p>Total PAUD: ${data.data.total_paud}</p>
                        <p>Total Kesetaraan: ${data.data.total_kesetaraan}</p>
                        <p>Akreditasi Data: ${data.data.akreditasi_rumpun.length} items</p>
                    `;
                } else {
                    testResults.api = false;
                    resultDiv.className = 'result status-error';
                    resultDiv.innerHTML = `<p class="error">✗ API Test: GAGAL</p><p>Error: ${data.message}</p>`;
                }
                updateFinalStatus();
            })
            .catch(error => {
                testResults.api = false;
                resultDiv.className = 'result status-error';
                resultDiv.innerHTML = `<p class="error">✗ API Test: GAGAL</p><p>Error: ${error.message}</p>`;
                updateFinalStatus();
            });
    }
    
    function testLibraries() {
        const resultDiv = document.getElementById('library-result');
        let html = '';
        let allOk = true;
        
        // Test jQuery
        if (typeof $ !== 'undefined') {
            html += '<p class="success">✓ jQuery: Loaded</p>';
        } else {
            html += '<p class="error">✗ jQuery: Not Loaded</p>';
            allOk = false;
        }
        
        // Test Chart.js
        if (typeof Chart !== 'undefined') {
            html += '<p class="success">✓ Chart.js: Loaded</p>';
        } else {
            html += '<p class="error">✗ Chart.js: Not Loaded</p>';
            allOk = false;
        }
        
        // Test DOM elements
        const testElements = ['total-dasmen', 'total-paud', 'total-kesetaraan'];
        let domOk = true;
        
        testElements.forEach(id => {
            if (document.getElementById(id)) {
                html += `<p class="success">✓ Element #${id}: Found</p>`;
            } else {
                html += `<p class="warning">⚠️ Element #${id}: Not Found (Normal untuk test page)</p>`;
            }
        });
        
        testResults.libraries = allOk;
        resultDiv.className = allOk ? 'result status-ok' : 'result status-error';
        resultDiv.innerHTML = html;
        updateFinalStatus();
    }
    
    function testDashboardIntegration() {
        const resultDiv = document.getElementById('integration-result');
        resultDiv.innerHTML = '<p class="info">Testing integration...</p>';
        
        // Test if we can simulate dashboard functions
        fetch('ajax/get_dashboard_data.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && typeof $ !== 'undefined' && typeof Chart !== 'undefined') {
                    // Simulate dashboard data processing
                    const dashboardData = data.data;
                    
                    // Test data validation
                    const hasValidData = (
                        Array.isArray(dashboardData.sekolah_dasmen) &&
                        Array.isArray(dashboardData.sekolah_paud) &&
                        Array.isArray(dashboardData.akreditasi_rumpun)
                    );
                    
                    if (hasValidData) {
                        testResults.integration = true;
                        resultDiv.className = 'result status-ok';
                        resultDiv.innerHTML = `
                            <p class="success">✓ Integration Test: BERHASIL</p>
                            <p>Data structure: Valid</p>
                            <p>Libraries: Ready</p>
                            <p>API: Working</p>
                            <p><strong>Dashboard siap digunakan!</strong></p>
                        `;
                    } else {
                        testResults.integration = false;
                        resultDiv.className = 'result status-error';
                        resultDiv.innerHTML = '<p class="error">✗ Integration Test: Data structure invalid</p>';
                    }
                } else {
                    testResults.integration = false;
                    resultDiv.className = 'result status-error';
                    resultDiv.innerHTML = '<p class="error">✗ Integration Test: API or libraries not working</p>';
                }
                updateFinalStatus();
            })
            .catch(error => {
                testResults.integration = false;
                resultDiv.className = 'result status-error';
                resultDiv.innerHTML = `<p class="error">✗ Integration Test: ${error.message}</p>`;
                updateFinalStatus();
            });
    }
    
    // Auto-run library test on page load
    $(document).ready(function() {
        testLibraries();
    });
    </script>
    
    <div class="box">
        <h2>📋 Quick Links</h2>
        <p>
            <a href="dashboard.php" target="_blank">→ Dashboard Utama</a> |
            <a href="test_simple.php">→ Test Simple</a> |
            <a href="test_dashboard_api.php">→ Test API Detail</a> |
            <a href="ajax/get_dashboard_data.php" target="_blank">→ Direct API</a>
        </p>
    </div>
    
</body>
</html>
