<?php
/**
 * File preview handler dengan security check untuk visitasi PAUD
 */

require_once '../../koneksi.php';
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

// Get parameters
$filename = $_GET['file'] ?? '';

if (empty($filename)) {
    http_response_code(400);
    die('File parameter required');
}

// Security: Validate filename (no path traversal)
if (strpos($filename, '..') !== false || strpos($filename, '/') !== false || strpos($filename, '\\') !== false) {
    http_response_code(403);
    die('Invalid filename');
}

// Get session variables
$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

// Verify file belongs to current asesor (check all file fields)
$check_query = "SELECT mv.* 
                FROM mapping_paud_visitasi mv
                WHERE (mv.file_pakta_integritas_1 = '$filename' 
                    OR mv.file_pakta_integritas_2 = '$filename'
                    OR mv.file_berita_acara_visitasi = '$filename'
                    OR mv.file_temuan_hasil_visitasi = '$filename'
                    OR mv.file_absen_pembuka = '$filename'
                    OR mv.file_absen_penutup = '$filename'
                    OR mv.file_foto_visitasi = '$filename'
                    OR mv.file_laporan_individu_1 = '$filename'
                    OR mv.file_laporan_individu_2 = '$filename'
                    OR mv.file_laporan_kelompok = '$filename'
                    OR mv.file_penjelasan_hasil_akreditasi = '$filename')
                    AND (mv.kd_asesor1 = '$kd_user' OR mv.kd_asesor2 = '$kd_user')
                    AND mv.provinsi_id = '$provinsi_id'";

$check_result = mysqli_query($conn, $check_query);

if (!$check_result || mysqli_num_rows($check_result) === 0) {
    http_response_code(403);
    die('Access denied');
}

// File path
$file_path = "../../../simak/files/upload_file_hasil_visitasi_paud/" . $filename;

// Check if file exists
if (!file_exists($file_path)) {
    http_response_code(404);
    die('File not found');
}

// Get file info
$file_size = filesize($file_path);
$file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

// Set appropriate headers
switch ($file_extension) {
    case 'pdf':
        $content_type = 'application/pdf';
        break;
    case 'doc':
        $content_type = 'application/msword';
        break;
    case 'docx':
        $content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        break;
    default:
        $content_type = 'application/octet-stream';
}

// Set headers for inline display
header('Content-Type: ' . $content_type);
header('Content-Length: ' . $file_size);
header('Content-Disposition: inline; filename="' . basename($filename) . '"');
header('Cache-Control: private, max-age=0, must-revalidate');
header('Pragma: public');

// Output file
readfile($file_path);
exit;
?>
