# Dashboard SIMAK - Troubleshooting Guide

## 🚨 Dashboard Tidak Menampilkan Data

### Langkah Debugging Sistematis

#### 1. **Test Session & Database Connection**
```
http://localhost/app-smkpdm/tim_it/dashboard/test_session.php
```
**Periksa:**
- ✅ Session `provinsi_id` ada dan valid
- ✅ Database connection berhasil
- ✅ Ada data sekolah di provinsi tersebut

#### 2. **Test Dashboard API**
```
http://localhost/app-smkpdm/tim_it/dashboard/ajax/get_dashboard_data.php
```
**Expected Response:**
```json
{
    "success": true,
    "data": {
        "sekolah_dasmen": [...],
        "total_dasmen": 10,
        "sekolah_paud": [...],
        "total_paud": 5,
        "sekolah_kesetaraan": [...],
        "total_kesetaraan": 2,
        "akreditasi_rumpun": [...],
        "akreditasi_jenjang": [...],
        "akreditasi_kota": [...]
    },
    "debug_info": {
        "provinsi_id": 64,
        "total_sekolah_provinsi": 17,
        "total_sekolah_dashboard": 17,
        "table_exists": false
    }
}
```

#### 3. **Debug Data Structure**
```
http://localhost/app-smkpdm/tim_it/dashboard/debug_data.php
```
**Periksa:**
- Struktur tabel sekolah
- Field rumpun dan jenjang_id
- Data sample

#### 4. **Test Individual Queries**
```
http://localhost/app-smkpdm/tim_it/dashboard/test_connection.php
```
**Periksa:**
- Query per rumpun
- Query alternatif
- Data akreditasi

### 🔧 Common Issues & Solutions

#### **Issue 1: Session provinsi_id tidak ada**
**Symptoms:** Error "Provinsi ID tidak ditemukan dalam session"
**Solution:**
1. Login ulang ke sistem
2. Periksa file `check_session.php`
3. Pastikan user memiliki provinsi_id yang valid

#### **Issue 2: Tidak ada data sekolah**
**Symptoms:** Total sekolah = 0
**Solution:**
1. Periksa tabel sekolah di database
2. Pastikan ada data dengan provinsi_id yang sesuai
3. Periksa field rumpun tidak NULL

#### **Issue 3: Query terlalu restrictive**
**Symptoms:** Data ada di database tapi tidak muncul
**Solution:**
Query sudah diperbaiki untuk lebih fleksibel:
- Removed `status_keaktifan_id` filter
- Removed `soft_delete` filter
- Added LIKE pattern matching untuk rumpun

#### **Issue 4: JavaScript Error**
**Symptoms:** Console error, chart tidak muncul
**Solution:**
1. Buka browser console (F12)
2. Periksa error JavaScript
3. Pastikan Chart.js loaded
4. Periksa response API valid

#### **Issue 5: Database Connection Error**
**Symptoms:** 500 Internal Server Error
**Solution:**
1. Periksa file `koneksi.php`
2. Pastikan database server running
3. Periksa credentials database
4. Periksa PHP error log

### 📊 Data Fallback Strategy

Jika tidak ada data real, sistem akan menggunakan:

1. **Fallback Query**: Query alternatif tanpa filter rumpun
2. **Smart Categorization**: Kategorisasi berdasarkan nama jenjang
3. **Minimal Data**: Data dummy minimal jika semua query gagal
4. **Proportional Akreditasi**: Data akreditasi proporsional (A=20%, B=50%, C=30%)

### 🛠️ Manual Testing Steps

#### **Step 1: Database Check**
```sql
-- Cek total sekolah
SELECT COUNT(*) FROM sekolah WHERE provinsi_id = 64;

-- Cek distribusi rumpun
SELECT rumpun, COUNT(*) FROM sekolah WHERE provinsi_id = 64 GROUP BY rumpun;

-- Cek jenjang
SELECT j.nm_jenjang, COUNT(s.sekolah_id) 
FROM sekolah s 
LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id 
WHERE s.provinsi_id = 64 
GROUP BY j.nm_jenjang;
```

#### **Step 2: API Test**
```javascript
// Test di browser console
fetch('/app-smkpdm/tim_it/dashboard/ajax/get_dashboard_data.php')
    .then(response => response.json())
    .then(data => console.log(data));
```

#### **Step 3: Session Check**
```php
// Tambahkan di dashboard.php untuk debug
<?php
echo "<script>console.log('Session:', " . json_encode($_SESSION) . ");</script>";
?>
```

### 📝 Log Files

**PHP Error Log:**
- Windows: `C:\xampp\apache\logs\error.log`
- Linux: `/var/log/apache2/error.log`

**Custom Logs:**
- Dashboard API logs error ke PHP error log
- Cari "Dashboard data request" untuk tracking

### 🔍 Debug Mode

Untuk enable debug mode, tambahkan di `get_dashboard_data.php`:
```php
// Enable debug mode
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

### 📞 Quick Fixes

#### **Fix 1: Reset Session**
```php
// Tambahkan di dashboard.php
session_destroy();
header('Location: ../../login.php');
```

#### **Fix 2: Force Fallback Data**
Edit `get_dashboard_data.php`, uncomment bagian minimal data.

#### **Fix 3: Bypass Provinsi Filter**
Temporary fix - comment out provinsi_id filter di query.

### ✅ Success Indicators

Dashboard berhasil jika:
- ✅ Total sekolah > 0 di semua rumpun
- ✅ Chart muncul dengan data
- ✅ No JavaScript errors di console
- ✅ API response success: true
- ✅ Debug info menunjukkan data valid

---
**Last Updated:** 2024-12-19
**Version:** 1.0.0
