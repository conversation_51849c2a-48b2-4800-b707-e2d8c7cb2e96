<?php
/**
 * Debug file untuk test upload functionality
 */

require_once '../../koneksi.php';
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

echo "<h2>Debug Upload Functionality</h2>";

// 1. Check session variables
echo "<h3>1. Session Variables:</h3>";
echo "kd_user: " . ($_SESSION['kd_user'] ?? 'NOT SET') . "<br>";
echo "provinsi_id: " . ($_SESSION['provinsi_id'] ?? 'NOT SET') . "<br>";
echo "level: " . ($_SESSION['level'] ?? 'NOT SET') . "<br>";

// 2. Check database connection
echo "<h3>2. Database Connection:</h3>";
if ($conn) {
    echo "✅ Database connected<br>";
} else {
    echo "❌ Database connection failed<br>";
}

// 3. Check mapping data
echo "<h3>3. Sample Mapping Data:</h3>";
$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

$query = "SELECT m.id_mapping, m.sekolah_id, s.nama_sekolah, m.file_laporan_hasil_kpa
          FROM mapping_paud_kpa m
          LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
          LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
          WHERE a.kd_asesor = ? AND m.provinsi_id = ?
          LIMIT 3";

$stmt = $conn->prepare($query);
if ($stmt) {
    $stmt->bind_param("si", $kd_user, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID Mapping</th><th>Sekolah ID</th><th>Nama Sekolah</th><th>File Upload</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id_mapping'] . "</td>";
            echo "<td>" . $row['sekolah_id'] . "</td>";
            echo "<td>" . $row['nama_sekolah'] . "</td>";
            echo "<td>" . ($row['file_laporan_hasil_kpa'] ?: 'EMPTY') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No mapping data found for this asesor<br>";
    }
} else {
    echo "❌ Query preparation failed<br>";
}

// 4. Check upload directory
echo "<h3>4. Upload Directory Check:</h3>";
$upload_dir = "../../simak/files/upload_file_hasil_kpa/";
echo "Upload directory path: " . realpath($upload_dir) . "<br>";

if (is_dir($upload_dir)) {
    echo "✅ Upload directory exists<br>";
    if (is_writable($upload_dir)) {
        echo "✅ Upload directory is writable<br>";
    } else {
        echo "❌ Upload directory is NOT writable<br>";
    }
} else {
    echo "❌ Upload directory does NOT exist<br>";
    echo "Attempting to create directory...<br>";
    if (mkdir($upload_dir, 0755, true)) {
        echo "✅ Directory created successfully<br>";
    } else {
        echo "❌ Failed to create directory<br>";
    }
}

// 5. Test file upload path
echo "<h3>5. File Path Test:</h3>";
$test_file = "test_" . time() . ".txt";
$test_path = $upload_dir . $test_file;
echo "Test file path: " . $test_path . "<br>";

if (file_put_contents($test_path, "Test content")) {
    echo "✅ Test file created successfully<br>";
    if (unlink($test_path)) {
        echo "✅ Test file deleted successfully<br>";
    } else {
        echo "⚠️ Test file created but couldn't delete<br>";
    }
} else {
    echo "❌ Failed to create test file<br>";
}

// 6. Check PHP upload settings
echo "<h3>6. PHP Upload Settings:</h3>";
echo "file_uploads: " . (ini_get('file_uploads') ? 'ON' : 'OFF') . "<br>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "max_execution_time: " . ini_get('max_execution_time') . "<br>";

// 7. Test AJAX endpoint
echo "<h3>7. AJAX Endpoint Test:</h3>";
$ajax_file = "ajax/upload_file.php";
if (file_exists($ajax_file)) {
    echo "✅ AJAX upload file exists<br>";
    if (is_readable($ajax_file)) {
        echo "✅ AJAX upload file is readable<br>";
    } else {
        echo "❌ AJAX upload file is NOT readable<br>";
    }
} else {
    echo "❌ AJAX upload file does NOT exist<br>";
}

// 8. Manual test form
echo "<h3>8. Manual Test Form:</h3>";
if (isset($_POST['test_upload']) && isset($_FILES['test_file'])) {
    echo "<h4>Upload Test Result:</h4>";
    echo "File name: " . $_FILES['test_file']['name'] . "<br>";
    echo "File size: " . $_FILES['test_file']['size'] . "<br>";
    echo "File type: " . $_FILES['test_file']['type'] . "<br>";
    echo "File error: " . $_FILES['test_file']['error'] . "<br>";
    echo "Temp name: " . $_FILES['test_file']['tmp_name'] . "<br>";
    
    if ($_FILES['test_file']['error'] === UPLOAD_ERR_OK) {
        $test_upload_path = $upload_dir . "manual_test_" . time() . ".pdf";
        if (move_uploaded_file($_FILES['test_file']['tmp_name'], $test_upload_path)) {
            echo "✅ Manual upload successful!<br>";
            echo "File saved to: " . $test_upload_path . "<br>";
        } else {
            echo "❌ Manual upload failed!<br>";
        }
    }
}
?>

<form method="POST" enctype="multipart/form-data">
    <label>Test Manual Upload:</label><br>
    <input type="file" name="test_file" accept=".pdf"><br><br>
    <button type="submit" name="test_upload">Test Upload</button>
</form>

<hr>
<p><a href="mapping_kpa.php">← Back to Mapping KPA</a></p>
