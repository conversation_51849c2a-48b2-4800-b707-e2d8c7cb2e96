<?php
/**
 * AJAX handler untuk upload file visitasi PAUD
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Asesor', '../../../login.php');

header('Content-Type: application/json');

// Set timezone
date_default_timezone_set('Asia/Singapore');

try {
    // Validasi request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method not allowed');
    }
    
    // Validasi input
    if (!isset($_POST['id_mapping']) || empty($_POST['id_mapping'])) {
        throw new Exception('ID Mapping tidak valid');
    }
    
    if (!isset($_POST['file_type']) || empty($_POST['file_type'])) {
        throw new Exception('File type tidak valid');
    }
    
    if (!isset($_FILES['file_visitasi']) || $_FILES['file_visitasi']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File tidak valid atau gagal diupload');
    }
    
    // Generate tanggal/jam di server
    $tanggal = mktime(date("m"),date("d"),date("Y"));
    $tglsekarang = date("Y-m-d", $tanggal);
    $jam = date("H:i:s");

    // Get data from POST
    $id_mapping = mysqli_real_escape_string($conn, $_POST['id_mapping']);
    $file_type = mysqli_real_escape_string($conn, $_POST['file_type']);
    $tgl_file = $tglsekarang;
    $jam_file = $jam;
    
    // Validasi file extension
    $allowed_extensions = ['pdf'];
    $file_extension = strtolower(pathinfo($_FILES['file_visitasi']['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_extensions)) {
        throw new Exception('Format file harus PDF');
    }
    
    // Cek apakah mapping exists dan milik asesor yang login
    $kd_user = mysqli_real_escape_string($conn, $_SESSION['kd_user'] ?? '');
    $provinsi_id = mysqli_real_escape_string($conn, $_SESSION['provinsi_id'] ?? '');
    
    $check_query = "SELECT mv.*, mv.kd_asesor1, mv.kd_asesor2
                    FROM mapping_paud_visitasi mv
                    WHERE mv.id_mapping = '$id_mapping' 
                        AND (mv.kd_asesor1 = '$kd_user' OR mv.kd_asesor2 = '$kd_user')
                        AND mv.provinsi_id = '$provinsi_id'";
    
    $check_result = mysqli_query($conn, $check_query);
    
    if (!$check_result || mysqli_num_rows($check_result) === 0) {
        throw new Exception('Data mapping tidak ditemukan atau tidak memiliki akses');
    }
    
    $mapping_data = mysqli_fetch_assoc($check_result);
    
    // Validasi akses berdasarkan file type dan asesor
    $asesor1_files = ['pakta_integritas_1', 'berita_acara_visitasi', 'temuan_hasil_visitasi', 
                      'absen_pembuka', 'absen_penutup', 'foto_visitasi', 'laporan_individu_1', 
                      'laporan_kelompok', 'penjelasan_hasil_akreditasi'];
    $asesor2_files = ['pakta_integritas_2', 'laporan_individu_2'];
    
    if (in_array($file_type, $asesor1_files) && $kd_user != $mapping_data['kd_asesor1']) {
        throw new Exception('Anda tidak memiliki akses untuk upload file ini');
    }
    
    if (in_array($file_type, $asesor2_files) && $kd_user != $mapping_data['kd_asesor2']) {
        throw new Exception('Anda tidak memiliki akses untuk upload file ini');
    }
    
    // Pastikan direktori upload exists
    $upload_dir = "../../../../simak/files/upload_file_hasil_visitasi_paud/";
    
    if (!is_dir($upload_dir) || !is_writable($upload_dir)) {
        throw new Exception('Direktori upload tidak tersedia atau tidak dapat ditulis');
    }
    
    // Get field name untuk database
    $field_mapping = [
        'pakta_integritas_1' => 'file_pakta_integritas_1',
        'pakta_integritas_2' => 'file_pakta_integritas_2',
        'berita_acara_visitasi' => 'file_berita_acara_visitasi',
        'temuan_hasil_visitasi' => 'file_temuan_hasil_visitasi',
        'absen_pembuka' => 'file_absen_pembuka',
        'absen_penutup' => 'file_absen_penutup',
        'foto_visitasi' => 'file_foto_visitasi',
        'laporan_individu_1' => 'file_laporan_individu_1',
        'laporan_individu_2' => 'file_laporan_individu_2',
        'laporan_kelompok' => 'file_laporan_kelompok',
        'penjelasan_hasil_akreditasi' => 'file_penjelasan_hasil_akreditasi'
    ];
    
    if (!isset($field_mapping[$file_type])) {
        throw new Exception('Jenis file tidak valid');
    }
    
    $db_field = $field_mapping[$file_type];
    
    // Hapus file lama jika ada
    if (!empty($mapping_data[$db_field])) {
        $old_file_path = $upload_dir . $mapping_data[$db_field];
        if (file_exists($old_file_path)) {
            unlink($old_file_path);
        }
    }
    
    // Generate nama file baru
    $temp = explode('.', $_FILES['file_visitasi']['name']);
    $nama_baru = round(microtime(true)) . '.' . end($temp);
    $nama_baru_escaped = mysqli_real_escape_string($conn, $nama_baru);

    // Upload file baru
    $upload_path = $upload_dir . $nama_baru;

    if (!move_uploaded_file($_FILES['file_visitasi']['tmp_name'], $upload_path)) {
        throw new Exception('Gagal mengupload file ke server');
    }

    // Update database dengan query langsung
    // Khusus untuk foto visitasi, update juga tanggal dan jam
    if ($file_type == 'foto_visitasi') {
        $update_query = "UPDATE mapping_paud_visitasi
                         SET $db_field = '$nama_baru_escaped',
                             tgl_file_foto_visitasi = '$tgl_file',
                             jam_file_foto_visitasi = '$jam_file'
                         WHERE id_mapping = '$id_mapping'";
    } else {
        $update_query = "UPDATE mapping_paud_visitasi
                         SET $db_field = '$nama_baru_escaped'
                         WHERE id_mapping = '$id_mapping'";
    }
    
    $update_result = mysqli_query($conn, $update_query);
    
    if (!$update_result) {
        // Jika update database gagal, hapus file yang sudah diupload
        if (file_exists($upload_path)) {
            unlink($upload_path);
        }
        $db_error = mysqli_error($conn);
        throw new Exception('Gagal menyimpan data ke database: ' . $db_error);
    }
    
    $affected_rows = mysqli_affected_rows($conn);
    
    if ($affected_rows === 0) {
        throw new Exception('Tidak ada data yang diupdate, periksa ID mapping');
    }
    
    // Response sukses
    echo json_encode([
        'success' => true,
        'message' => 'File berhasil di-upload',
        'data' => [
            'id_mapping' => $id_mapping,
            'file_type' => $file_type,
            'filename' => $nama_baru,
            'upload_date' => $tgl_file,
            'upload_time' => $jam_file
        ]
    ]);
    
} catch (Exception $e) {
    // Response error
    echo json_encode([
        'success' => false,
        'message' => 'File gagal di-upload, silahkan upload ulang',
        'error' => $e->getMessage()
    ]);
}
?>
