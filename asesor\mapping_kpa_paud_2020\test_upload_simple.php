<?php
/**
 * Simple test untuk upload functionality
 */

require_once '../../koneksi.php';
require_once '../../check_session.php';
requireLevel('Asesor', '../../login.php');

// Get sample mapping ID
$kd_user = $_SESSION['kd_user'] ?? '';
$provinsi_id = $_SESSION['provinsi_id'] ?? '';

$query = "SELECT m.id_mapping, s.nama_sekolah, m.file_laporan_hasil_kpa
          FROM mapping_paud_kpa m
          LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
          LEFT JOIN asesor a ON m.kd_asesor = a.kd_asesor
          WHERE a.kd_asesor = '$kd_user' AND m.provinsi_id = '$provinsi_id'
          LIMIT 1";

$result = mysqli_query($conn, $query);
$sample_mapping = mysqli_fetch_assoc($result);

echo "<h2>🧪 Simple Upload Test</h2>";
echo "<p><strong>Session kd_user:</strong> $kd_user</p>";
echo "<p><strong>Session provinsi_id:</strong> $provinsi_id</p>";

if ($sample_mapping) {
    echo "<p><strong>Test Mapping ID:</strong> " . $sample_mapping['id_mapping'] . "</p>";
    echo "<p><strong>Sekolah:</strong> " . $sample_mapping['nama_sekolah'] . "</p>";
    echo "<p><strong>Current File:</strong> " . ($sample_mapping['file_laporan_hasil_kpa'] ?: 'EMPTY') . "</p>";
    
    // Manual test form
    if (isset($_POST['test_upload']) && isset($_FILES['test_file'])) {
        echo "<hr><h3>📤 Upload Test Result:</h3>";
        
        // Simulate AJAX upload process
        $id_mapping = $sample_mapping['id_mapping'];
        $tgl_file_hasil_kpa = date('Y-m-d');
        $jam_file_hasil_kpa = date('H:i:s');
        
        if ($_FILES['test_file']['error'] === UPLOAD_ERR_OK) {
            // Generate filename
            $temp = explode('.', $_FILES['test_file']['name']);
            $nama_baru = round(microtime(true)) . '.' . end($temp);
            
            // Upload directory
            $upload_dir = "../../../simak/files/upload_file_hasil_kpa/";
            $upload_path = $upload_dir . $nama_baru;
            
            echo "<p>📁 Upload path: $upload_path</p>";
            
            // Move file
            if (move_uploaded_file($_FILES['test_file']['tmp_name'], $upload_path)) {
                echo "<p style='color: green;'>✅ File uploaded successfully!</p>";
                
                // Update database
                $nama_baru_escaped = mysqli_real_escape_string($conn, $nama_baru);
                $update_query = "UPDATE mapping_paud_kpa 
                                 SET file_laporan_hasil_kpa = '$nama_baru_escaped', 
                                     tgl_file_hasil_kpa = '$tgl_file_hasil_kpa', 
                                     jam_file_hasil_kpa = '$jam_file_hasil_kpa'
                                 WHERE id_mapping = '$id_mapping'";
                
                echo "<p>🗄️ Query: $update_query</p>";
                
                $update_result = mysqli_query($conn, $update_query);
                
                if ($update_result) {
                    $affected_rows = mysqli_affected_rows($conn);
                    echo "<p style='color: green;'>✅ Database updated! Affected rows: $affected_rows</p>";
                    
                    // Verify update
                    $verify_query = "SELECT file_laporan_hasil_kpa, tgl_file_hasil_kpa, jam_file_hasil_kpa 
                                     FROM mapping_paud_kpa WHERE id_mapping = '$id_mapping'";
                    $verify_result = mysqli_query($conn, $verify_query);
                    $verify_data = mysqli_fetch_assoc($verify_result);
                    
                    echo "<p><strong>Verification:</strong></p>";
                    echo "<ul>";
                    echo "<li>File: " . $verify_data['file_laporan_hasil_kpa'] . "</li>";
                    echo "<li>Date: " . $verify_data['tgl_file_hasil_kpa'] . "</li>";
                    echo "<li>Time: " . $verify_data['jam_file_hasil_kpa'] . "</li>";
                    echo "</ul>";
                    
                } else {
                    echo "<p style='color: red;'>❌ Database update failed: " . mysqli_error($conn) . "</p>";
                }
                
            } else {
                echo "<p style='color: red;'>❌ File upload failed!</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ File error: " . $_FILES['test_file']['error'] . "</p>";
        }
    }
    
    ?>
    <hr>
    <h3>📤 Manual Upload Test:</h3>
    <form method="POST" enctype="multipart/form-data">
        <p>
            <label>Select PDF file:</label><br>
            <input type="file" name="test_file" accept=".pdf" required>
        </p>
        <p>
            <button type="submit" name="test_upload" style="padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 3px;">
                🧪 Test Upload
            </button>
        </p>
    </form>
    <?php
    
} else {
    echo "<p style='color: red;'>❌ No mapping data found for testing</p>";
}
?>

<hr>
<p><a href="mapping_kpa.php">← Back to Mapping KPA</a></p>
