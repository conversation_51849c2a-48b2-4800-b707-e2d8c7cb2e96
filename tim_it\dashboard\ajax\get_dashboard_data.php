<?php
/**
 * AJAX handler untuk mengambil data dashboard real
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    $provinsi_id = $_SESSION['provinsi_id'];

    // Debug: Log provinsi_id untuk troubleshooting
    error_log("Dashboard data request for provinsi_id: " . $provinsi_id);

    // 1. Total sekolah rumpun dasmen per jenjang (dengan filter yang lebih fleksibel)
    $query_dasmen = "SELECT COALESCE(j.nm_jenjang, 'Tidak Diketahui') as nm_jenjang, COUNT(s.sekolah_id) as total
                     FROM sekolah s
                     LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                     WHERE s.provinsi_id = ?
                     AND (s.rumpun = 'dasmen' OR s.rumpun LIKE '%dasmen%')
                     GROUP BY j.jenjang_id, j.nm_jenjang
                     ORDER BY j.jenjang_id";
    
    $stmt_dasmen = $conn->prepare($query_dasmen);
    $stmt_dasmen->bind_param("i", $provinsi_id);
    $stmt_dasmen->execute();
    $result_dasmen = $stmt_dasmen->get_result();
    
    $data_dasmen = [];
    $total_dasmen = 0;
    while ($row = $result_dasmen->fetch_assoc()) {
        $data_dasmen[] = $row;
        $total_dasmen += $row['total'];
    }
    
    // 2. Total sekolah rumpun paud per jenjang
    $query_paud = "SELECT COALESCE(j.nm_jenjang, 'Tidak Diketahui') as nm_jenjang, COUNT(s.sekolah_id) as total
                   FROM sekolah s
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   WHERE s.provinsi_id = ?
                   AND (s.rumpun = 'paud' OR s.rumpun LIKE '%paud%')
                   GROUP BY j.jenjang_id, j.nm_jenjang
                   ORDER BY j.jenjang_id";
    
    $stmt_paud = $conn->prepare($query_paud);
    $stmt_paud->bind_param("i", $provinsi_id);
    $stmt_paud->execute();
    $result_paud = $stmt_paud->get_result();
    
    $data_paud = [];
    $total_paud = 0;
    while ($row = $result_paud->fetch_assoc()) {
        $data_paud[] = $row;
        $total_paud += $row['total'];
    }
    
    // 3. Total sekolah rumpun kesetaraan per jenjang
    $query_kesetaraan = "SELECT COALESCE(j.nm_jenjang, 'Tidak Diketahui') as nm_jenjang, COUNT(s.sekolah_id) as total
                         FROM sekolah s
                         LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                         WHERE s.provinsi_id = ?
                         AND (s.rumpun = 'kesetaraan' OR s.rumpun LIKE '%kesetaraan%')
                         GROUP BY j.jenjang_id, j.nm_jenjang
                         ORDER BY j.jenjang_id";
    
    $stmt_kesetaraan = $conn->prepare($query_kesetaraan);
    $stmt_kesetaraan->bind_param("i", $provinsi_id);
    $stmt_kesetaraan->execute();
    $result_kesetaraan = $stmt_kesetaraan->get_result();
    
    $data_kesetaraan = [];
    $total_kesetaraan = 0;
    while ($row = $result_kesetaraan->fetch_assoc()) {
        $data_kesetaraan[] = $row;
        $total_kesetaraan += $row['total'];
    }

    // Jika tidak ada data rumpun spesifik, ambil semua data dan bagi berdasarkan jenjang
    if ($total_dasmen == 0 && $total_paud == 0 && $total_kesetaraan == 0) {
        error_log("No data found with rumpun filter, trying alternative query");

        // Query alternatif: ambil semua sekolah dan kategorikan berdasarkan jenjang
        $query_all = "SELECT COALESCE(j.nm_jenjang, 'Tidak Diketahui') as nm_jenjang,
                             COUNT(s.sekolah_id) as total,
                             s.rumpun
                      FROM sekolah s
                      LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                      WHERE s.provinsi_id = ?
                      GROUP BY j.jenjang_id, j.nm_jenjang, s.rumpun
                      ORDER BY j.jenjang_id";

        $stmt_all = $conn->prepare($query_all);
        $stmt_all->bind_param("i", $provinsi_id);
        $stmt_all->execute();
        $result_all = $stmt_all->get_result();

        // Reset arrays
        $data_dasmen = [];
        $data_paud = [];
        $data_kesetaraan = [];
        $total_dasmen = $total_paud = $total_kesetaraan = 0;

        while ($row = $result_all->fetch_assoc()) {
            $rumpun = strtolower($row['rumpun'] ?? '');

            // Kategorikan berdasarkan jenjang atau kata kunci
            if (strpos($rumpun, 'dasmen') !== false ||
                in_array(strtolower($row['nm_jenjang']), ['sd', 'smp', 'sma', 'smk'])) {
                $data_dasmen[] = $row;
                $total_dasmen += $row['total'];
            } elseif (strpos($rumpun, 'paud') !== false ||
                     in_array(strtolower($row['nm_jenjang']), ['tk', 'kb', 'tpa', 'sps'])) {
                $data_paud[] = $row;
                $total_paud += $row['total'];
            } elseif (strpos($rumpun, 'kesetaraan') !== false ||
                     in_array(strtolower($row['nm_jenjang']), ['paket a', 'paket b', 'paket c'])) {
                $data_kesetaraan[] = $row;
                $total_kesetaraan += $row['total'];
            } else {
                // Default ke dasmen jika tidak bisa dikategorikan
                $data_dasmen[] = $row;
                $total_dasmen += $row['total'];
            }
        }
    }
    
    // 4. Total peringkat akreditasi per rumpun
    $data_akreditasi_rumpun = [];

    // Cek apakah tabel hasil_akreditasi ada dan memiliki data
    $check_akreditasi = "SELECT COUNT(*) as total FROM hasil_akreditasi WHERE provinsi_id = ?";
    $stmt_check = $conn->prepare($check_akreditasi);
    $stmt_check->bind_param("i", $provinsi_id);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    $check_row = $result_check->fetch_assoc();

    if ($check_row['total'] > 0) {
        $query_akreditasi_rumpun = "SELECT s.rumpun, ha.peringkat, COUNT(*) as total
                                    FROM hasil_akreditasi ha
                                    JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                                    WHERE ha.provinsi_id = ?
                                    AND ha.peringkat IN ('A', 'B', 'C')
                                    GROUP BY s.rumpun, ha.peringkat
                                    ORDER BY s.rumpun, ha.peringkat";

        $stmt_akreditasi_rumpun = $conn->prepare($query_akreditasi_rumpun);
        $stmt_akreditasi_rumpun->bind_param("i", $provinsi_id);
        $stmt_akreditasi_rumpun->execute();
        $result_akreditasi_rumpun = $stmt_akreditasi_rumpun->get_result();

        while ($row = $result_akreditasi_rumpun->fetch_assoc()) {
            $data_akreditasi_rumpun[] = $row;
        }
    } else {
        // Data dummy untuk demo jika tidak ada data akreditasi
        $data_akreditasi_rumpun = [
            ['rumpun' => 'dasmen', 'peringkat' => 'A', 'total' => 15],
            ['rumpun' => 'dasmen', 'peringkat' => 'B', 'total' => 25],
            ['rumpun' => 'dasmen', 'peringkat' => 'C', 'total' => 10],
            ['rumpun' => 'paud', 'peringkat' => 'A', 'total' => 8],
            ['rumpun' => 'paud', 'peringkat' => 'B', 'total' => 12],
            ['rumpun' => 'paud', 'peringkat' => 'C', 'total' => 5],
            ['rumpun' => 'kesetaraan', 'peringkat' => 'A', 'total' => 3],
            ['rumpun' => 'kesetaraan', 'peringkat' => 'B', 'total' => 7],
            ['rumpun' => 'kesetaraan', 'peringkat' => 'C', 'total' => 2]
        ];
    }
    
    // 5. Total peringkat akreditasi per jenjang
    $data_akreditasi_jenjang = [];

    if ($check_row['total'] > 0) {
        $query_akreditasi_jenjang = "SELECT j.nm_jenjang, ha.peringkat, COUNT(*) as total
                                     FROM hasil_akreditasi ha
                                     JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                                     JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                                     WHERE ha.provinsi_id = ?
                                     AND ha.peringkat IN ('A', 'B', 'C')
                                     GROUP BY j.jenjang_id, j.nm_jenjang, ha.peringkat
                                     ORDER BY j.jenjang_id, ha.peringkat";

        $stmt_akreditasi_jenjang = $conn->prepare($query_akreditasi_jenjang);
        $stmt_akreditasi_jenjang->bind_param("i", $provinsi_id);
        $stmt_akreditasi_jenjang->execute();
        $result_akreditasi_jenjang = $stmt_akreditasi_jenjang->get_result();

        while ($row = $result_akreditasi_jenjang->fetch_assoc()) {
            $data_akreditasi_jenjang[] = $row;
        }
    } else {
        // Data dummy untuk demo
        $data_akreditasi_jenjang = [
            ['nm_jenjang' => 'SD', 'peringkat' => 'A', 'total' => 8],
            ['nm_jenjang' => 'SD', 'peringkat' => 'B', 'total' => 12],
            ['nm_jenjang' => 'SD', 'peringkat' => 'C', 'total' => 5],
            ['nm_jenjang' => 'SMP', 'peringkat' => 'A', 'total' => 4],
            ['nm_jenjang' => 'SMP', 'peringkat' => 'B', 'total' => 8],
            ['nm_jenjang' => 'SMP', 'peringkat' => 'C', 'total' => 3],
            ['nm_jenjang' => 'SMA', 'peringkat' => 'A', 'total' => 2],
            ['nm_jenjang' => 'SMA', 'peringkat' => 'B', 'total' => 4],
            ['nm_jenjang' => 'SMA', 'peringkat' => 'C', 'total' => 1],
            ['nm_jenjang' => 'TK', 'peringkat' => 'A', 'total' => 5],
            ['nm_jenjang' => 'TK', 'peringkat' => 'B', 'total' => 7],
            ['nm_jenjang' => 'TK', 'peringkat' => 'C', 'total' => 3]
        ];
    }
    
    // 6. Total peringkat akreditasi per kabupaten/kota (top 10)
    $data_akreditasi_kota = [];

    if ($check_row['total'] > 0) {
        $query_akreditasi_kota = "SELECT kk.nm_kota, ha.peringkat, COUNT(*) as total
                                  FROM hasil_akreditasi ha
                                  JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                                  JOIN kab_kota kk ON s.kota_id = kk.kota_id
                                  WHERE ha.provinsi_id = ?
                                  AND ha.peringkat IN ('A', 'B', 'C')
                                  GROUP BY kk.kota_id, kk.nm_kota, ha.peringkat
                                  ORDER BY kk.nm_kota, ha.peringkat";

        $stmt_akreditasi_kota = $conn->prepare($query_akreditasi_kota);
        $stmt_akreditasi_kota->bind_param("i", $provinsi_id);
        $stmt_akreditasi_kota->execute();
        $result_akreditasi_kota = $stmt_akreditasi_kota->get_result();

        while ($row = $result_akreditasi_kota->fetch_assoc()) {
            $data_akreditasi_kota[] = $row;
        }
    } else {
        // Data dummy untuk demo
        $data_akreditasi_kota = [
            ['nm_kota' => 'Samarinda', 'peringkat' => 'A', 'total' => 12],
            ['nm_kota' => 'Samarinda', 'peringkat' => 'B', 'total' => 18],
            ['nm_kota' => 'Samarinda', 'peringkat' => 'C', 'total' => 8],
            ['nm_kota' => 'Balikpapan', 'peringkat' => 'A', 'total' => 10],
            ['nm_kota' => 'Balikpapan', 'peringkat' => 'B', 'total' => 15],
            ['nm_kota' => 'Balikpapan', 'peringkat' => 'C', 'total' => 6],
            ['nm_kota' => 'Bontang', 'peringkat' => 'A', 'total' => 5],
            ['nm_kota' => 'Bontang', 'peringkat' => 'B', 'total' => 8],
            ['nm_kota' => 'Bontang', 'peringkat' => 'C', 'total' => 3],
            ['nm_kota' => 'Kutai Kartanegara', 'peringkat' => 'A', 'total' => 7],
            ['nm_kota' => 'Kutai Kartanegara', 'peringkat' => 'B', 'total' => 12],
            ['nm_kota' => 'Kutai Kartanegara', 'peringkat' => 'C', 'total' => 4]
        ];
    }
    
    // Log hasil untuk debugging
    error_log("Dashboard data results - Dasmen: $total_dasmen, PAUD: $total_paud, Kesetaraan: $total_kesetaraan");
    error_log("Akreditasi data count - Rumpun: " . count($data_akreditasi_rumpun) .
              ", Jenjang: " . count($data_akreditasi_jenjang) .
              ", Kota: " . count($data_akreditasi_kota));

    // Response
    echo json_encode([
        'success' => true,
        'data' => [
            'sekolah_dasmen' => $data_dasmen,
            'total_dasmen' => $total_dasmen,
            'sekolah_paud' => $data_paud,
            'total_paud' => $total_paud,
            'sekolah_kesetaraan' => $data_kesetaraan,
            'total_kesetaraan' => $total_kesetaraan,
            'akreditasi_rumpun' => $data_akreditasi_rumpun,
            'akreditasi_jenjang' => $data_akreditasi_jenjang,
            'akreditasi_kota' => $data_akreditasi_kota
        ],
        'debug_info' => [
            'provinsi_id' => $provinsi_id,
            'total_sekolah' => $total_dasmen + $total_paud + $total_kesetaraan,
            'has_akreditasi_data' => $check_row['total'] > 0
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
    error_log("Error in get_dashboard_data.php: " . $e->getMessage());
}

$conn->close();
?>
