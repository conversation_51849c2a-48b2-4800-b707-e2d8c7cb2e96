<?php
/**
 * Test file untuk memastikan koneksi database dan query dashboard ber<PERSON><PERSON> dengan baik
 */

// Include koneksi database
require_once '../../koneksi.php';

// Include session checker
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

echo "<h2>Test Dashboard Database Connection</h2>";

try {
    $provinsi_id = $_SESSION['provinsi_id'];
    echo "<p><strong>Provinsi ID Session:</strong> $provinsi_id</p>";
    
    // Test 1: Total sekolah dasmen
    echo "<h3>1. Test Sekolah Dasmen per Jenjang</h3>";
    $query_dasmen = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
                     FROM sekolah s
                     LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                     WHERE s.provinsi_id = ? 
                     AND s.rumpun = 'dasmen'
                     AND s.status_keaktifan_id = '1'
                     AND (s.soft_delete IS NULL OR s.soft_delete != '1')
                     GROUP BY j.jenjang_id, j.nm_jenjang
                     ORDER BY j.jenjang_id";
    
    $stmt = $conn->prepare($query_dasmen);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<table border='1'>";
    echo "<tr><th>Jenjang</th><th>Total</th></tr>";
    $total_dasmen = 0;
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . ($row['nm_jenjang'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
        $total_dasmen += $row['total'];
    }
    echo "</table>";
    echo "<p><strong>Total Dasmen:</strong> $total_dasmen</p>";
    
    // Test 2: Total sekolah paud
    echo "<h3>2. Test Sekolah PAUD per Jenjang</h3>";
    $query_paud = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
                   FROM sekolah s
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   WHERE s.provinsi_id = ? 
                   AND s.rumpun = 'paud'
                   AND s.status_keaktifan_id = '1'
                   AND (s.soft_delete IS NULL OR s.soft_delete != '1')
                   GROUP BY j.jenjang_id, j.nm_jenjang
                   ORDER BY j.jenjang_id";
    
    $stmt = $conn->prepare($query_paud);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<table border='1'>";
    echo "<tr><th>Jenjang</th><th>Total</th></tr>";
    $total_paud = 0;
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . ($row['nm_jenjang'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
        $total_paud += $row['total'];
    }
    echo "</table>";
    echo "<p><strong>Total PAUD:</strong> $total_paud</p>";
    
    // Test 3: Total sekolah kesetaraan
    echo "<h3>3. Test Sekolah Kesetaraan per Jenjang</h3>";
    $query_kesetaraan = "SELECT j.nm_jenjang, COUNT(s.sekolah_id) as total
                         FROM sekolah s
                         LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                         WHERE s.provinsi_id = ? 
                         AND s.rumpun = 'kesetaraan'
                         AND s.status_keaktifan_id = '1'
                         AND (s.soft_delete IS NULL OR s.soft_delete != '1')
                         GROUP BY j.jenjang_id, j.nm_jenjang
                         ORDER BY j.jenjang_id";
    
    $stmt = $conn->prepare($query_kesetaraan);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<table border='1'>";
    echo "<tr><th>Jenjang</th><th>Total</th></tr>";
    $total_kesetaraan = 0;
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . ($row['nm_jenjang'] ?? 'NULL') . "</td><td>" . $row['total'] . "</td></tr>";
        $total_kesetaraan += $row['total'];
    }
    echo "</table>";
    echo "<p><strong>Total Kesetaraan:</strong> $total_kesetaraan</p>";
    
    // Test 4: Akreditasi per rumpun
    echo "<h3>4. Test Akreditasi per Rumpun</h3>";
    $query_akreditasi_rumpun = "SELECT s.rumpun, ha.peringkat, COUNT(*) as total
                                FROM hasil_akreditasi ha
                                JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                                WHERE ha.provinsi_id = ?
                                AND ha.peringkat IN ('A', 'B', 'C')
                                AND s.status_keaktifan_id = '1'
                                AND (s.soft_delete IS NULL OR s.soft_delete != '1')
                                GROUP BY s.rumpun, ha.peringkat
                                ORDER BY s.rumpun, ha.peringkat";
    
    $stmt = $conn->prepare($query_akreditasi_rumpun);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<table border='1'>";
    echo "<tr><th>Rumpun</th><th>Peringkat</th><th>Total</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . $row['rumpun'] . "</td><td>" . $row['peringkat'] . "</td><td>" . $row['total'] . "</td></tr>";
    }
    echo "</table>";
    
    echo "<h3>✅ Semua Test Berhasil!</h3>";
    echo "<p>Database connection dan query berjalan dengan baik.</p>";
    echo "<p><a href='dashboard.php'>← Kembali ke Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
}

$conn->close();
?>
